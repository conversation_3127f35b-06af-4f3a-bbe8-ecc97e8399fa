<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    if (auth()->check()) {
        return redirect()->route('dashboard.index');
    }
    return redirect()->route('auth.login');
});

// Routes pour les pages principales (temporaires - seront remplacées par les modules)
Route::middleware(['web', 'auth'])->group(function () {
    // Routes temporaires pour les liens du menu
    Route::get('/affiliation', function () {
        return redirect()->route('adherents.index');
    })->name('affiliation.index');

    Route::get('/declaration', function () {
        return redirect()->route('declarations.index');
    })->name('declaration.index');

    Route::get('/pre-liquidation', function () {
        return redirect()->route('pre-liquidations.index');
    })->name('pre-liquidation.index');

    Route::get('/statistiques', [App\Http\Controllers\StatistiquesController::class, 'index'])->name('statistiques.index');
    Route::get('/api/statistiques/chart-data', [App\Http\Controllers\StatistiquesController::class, 'getChartData'])->name('statistiques.api.chart-data');
    Route::get('/statistiques/export', [App\Http\Controllers\StatistiquesController::class, 'export'])->name('statistiques.export');

    // Routes pour les paramètres système
    Route::get('/parametres', function () {
        return view('settings.dashboard');
    })->name('parametres.index');
    Route::get('/parametres/systeme', [App\Http\Controllers\SettingsController::class, 'index'])->name('settings.index');
    Route::get('/parametres/test', function () {
        $settings = ['app_name' => 'CRFM', 'app_description' => 'Test'];
        return view('settings.test', compact('settings'));
    })->name('settings.test');

    Route::get('/parametres/debug', function () {
        return response()->json([
            'message' => 'Debug route works',
            'laravel_version' => app()->version(),
            'php_version' => PHP_VERSION,
            'errors_available' => session()->has('errors'),
            'errors_type' => session()->has('errors') ? get_class(session()->get('errors')) : 'none'
        ]);
    });
    Route::put('/parametres/systeme', [App\Http\Controllers\SettingsController::class, 'update'])->name('settings.update');
    Route::post('/parametres/systeme/clear-cache', [App\Http\Controllers\SettingsController::class, 'clearCache'])->name('settings.clear-cache');
    Route::get('/parametres/systeme/export', [App\Http\Controllers\SettingsController::class, 'export'])->name('settings.export');
    Route::post('/parametres/systeme/import', [App\Http\Controllers\SettingsController::class, 'import'])->name('settings.import');
    Route::post('/parametres/systeme/test-email', [App\Http\Controllers\SettingsController::class, 'testEmail'])->name('settings.test-email');

    // Routes pour la gestion des rôles et permissions
    Route::get('/parametres/roles', [App\Http\Controllers\RoleController::class, 'index'])->name('roles.index');
    Route::post('/parametres/roles/permissions', [App\Http\Controllers\RoleController::class, 'updatePermissions'])->name('roles.update-permissions');

    // Routes pour les paramètres de sécurité
    Route::get('/parametres/securite', [App\Http\Controllers\SecurityController::class, 'index'])->name('security.index');
    Route::put('/parametres/securite', [App\Http\Controllers\SecurityController::class, 'update'])->name('security.update');
    Route::post('/parametres/securite/force-password-reset', [App\Http\Controllers\SecurityController::class, 'forcePasswordReset'])->name('security.force-password-reset');
    Route::post('/parametres/securite/clear-sessions', [App\Http\Controllers\SecurityController::class, 'clearAllSessions'])->name('security.clear-sessions');
    Route::get('/parametres/securite/report', [App\Http\Controllers\SecurityController::class, 'generateReport'])->name('security.report');

    // Routes pour la gestion des notifications
    Route::get('/parametres/notifications', [App\Http\Controllers\NotificationController::class, 'index'])->name('notifications.index');
    Route::put('/parametres/notifications', [App\Http\Controllers\NotificationController::class, 'update'])->name('notifications.update');
    Route::post('/parametres/notifications/test-email', [App\Http\Controllers\NotificationController::class, 'testEmail'])->name('notifications.test-email');
    Route::post('/parametres/notifications/test-sms', [App\Http\Controllers\NotificationController::class, 'testSms'])->name('notifications.test-sms');
    Route::post('/parametres/notifications/bulk', [App\Http\Controllers\NotificationController::class, 'sendBulk'])->name('notifications.bulk');

    // Redirection pour compatibilité
    Route::get('/parametres/users', function () {
        return redirect()->route('users.index');
    })->name('parametres.users');

    // Routes pour la gestion des utilisateurs
    Route::resource('users', App\Http\Controllers\UserController::class);
    Route::post('/users/{user}/toggle-status', [App\Http\Controllers\UserController::class, 'toggleStatus'])->name('users.toggle-status');
    Route::post('/users/{user}/reset-password', [App\Http\Controllers\UserController::class, 'resetPassword'])->name('users.reset-password');
    Route::post('/users/{id}/restore', [App\Http\Controllers\UserController::class, 'restore'])->name('users.restore');

    Route::get('/outils', [App\Http\Controllers\OutilsController::class, 'index'])->name('outils.index');
    Route::post('/api/outils/calculer-pension', [App\Http\Controllers\OutilsController::class, 'calculerPension'])->name('outils.api.calculer-pension');
    Route::post('/api/outils/generer-numero', [App\Http\Controllers\OutilsController::class, 'genererNumero'])->name('outils.api.generer-numero');
    Route::get('/api/outils/verifier-donnees', [App\Http\Controllers\OutilsController::class, 'verifierDonnees'])->name('outils.api.verifier-donnees');
    Route::post('/outils/importer', [App\Http\Controllers\OutilsController::class, 'importerDonnees'])->name('outils.importer');
    Route::get('/outils/exporter', [App\Http\Controllers\OutilsController::class, 'exporterDonnees'])->name('outils.exporter');

    // API Routes
    Route::get('/api/dashboard/stats', function () {
        return response()->json([
            'success' => true,
            'data' => [
                'total_adherents' => rand(8700, 8800),
                'cotisations_mois' => rand(15000000, 16000000),
                'pensions_en_cours' => rand(40, 50),
                'utilisateurs_actifs' => rand(5, 10)
            ]
        ]);
    })->name('api.dashboard.stats');

    // API pour l'historique des cotisations d'un adhérent
    Route::get('/api/adherents/{adherent}/cotisations-history', function ($adherentId) {
        // Simulation de données - à remplacer par la vraie logique
        return response()->json([
            [
                'periode' => 'Jan 2024',
                'montant' => '45,000',
                'status' => 'Payée',
                'status_color' => 'success'
            ],
            [
                'periode' => 'Déc 2023',
                'montant' => '45,000',
                'status' => 'Payée',
                'status_color' => 'success'
            ],
            [
                'periode' => 'Nov 2023',
                'montant' => '42,000',
                'status' => 'En retard',
                'status_color' => 'danger'
            ]
        ]);
    })->name('api.adherents.cotisations-history');

    // API pour les informations de pension d'un adhérent
    Route::get('/api/adherents/{adherent}/pension-info', function ($adherentId) {
        // Simulation de données - à remplacer par la vraie logique
        return response()->json([
            'success' => true,
            'salaire_base' => 250000,
            'annees_service' => 25,
            'age' => 58,
            'eligible_retraite' => true
        ]);
    })->name('api.adherents.pension-info');
});
