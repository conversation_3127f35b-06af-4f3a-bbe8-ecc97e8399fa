<?php

use Illuminate\Support\Facades\Route;
use Modules\Adherents\Controllers\AdherentController;

/*
|--------------------------------------------------------------------------
| Adherents Module Routes
|--------------------------------------------------------------------------
*/

Route::middleware(['web', 'auth'])->group(function () {

    // Routes spécifiques (DOIVENT être avant les routes avec paramètres)
    Route::get('/adherents/export', [AdherentController::class, 'export'])->name('adherents.export');
    Route::get('/adherents/import/form', [AdherentController::class, 'showImport'])->name('adherents.import.form');
    Route::post('/adherents/import', [AdherentController::class, 'import'])->name('adherents.import');
    Route::get('/adherents/create', [AdherentController::class, 'create'])->name('adherents.create');

    // Routes de base CRUD
    Route::get('/adherents', [AdherentController::class, 'index'])->name('adherents.index');
    Route::post('/adherents', [AdherentController::class, 'store'])->name('adherents.store');
    Route::get('/adherents/{adherent}', [AdherentController::class, 'show'])->name('adherents.show');
    Route::get('/adherents/{adherent}/edit', [AdherentController::class, 'edit'])->name('adherents.edit');
    Route::put('/adherents/{adherent}', [AdherentController::class, 'update'])->name('adherents.update');
    Route::delete('/adherents/{adherent}', [AdherentController::class, 'destroy'])->name('adherents.destroy');

    // Status management
    Route::patch('/adherents/{adherent}/suspend', [AdherentController::class, 'suspend'])->name('adherents.suspend');
    Route::patch('/adherents/{adherent}/reactivate', [AdherentController::class, 'reactivate'])->name('adherents.reactivate');

    // Document management
    Route::post('/adherents/{adherent}/documents', [AdherentController::class, 'uploadDocument'])->name('adherents.documents.upload');

});