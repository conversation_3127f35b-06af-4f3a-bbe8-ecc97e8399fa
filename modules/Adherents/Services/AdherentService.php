<?php

namespace Modules\Adherents\Services;

use Modules\Adherents\Models\Adherent;
use Modules\Adherents\Repositories\AdherentRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class AdherentService
{
    protected AdherentRepository $adherentRepository;

    public function __construct(AdherentRepository $adherentRepository)
    {
        $this->adherentRepository = $adherentRepository;
    }

    /**
     * Create new adherent
     */
    public function createAdherent(array $data): Adherent
    {
        DB::beginTransaction();
        
        try {
            // Generate adherent number if not provided
            if (empty($data['numero_adherent'])) {
                $data['numero_adherent'] = $this->adherentRepository->generateNextNumero();
            }

            $adherent = $this->adherentRepository->create($data);

            // Create beneficiaires if provided
            if (!empty($data['beneficiaires'])) {
                $this->createBeneficiaires($adherent, $data['beneficiaires']);
            }

            DB::commit();

            Log::info('Adherent created', [
                'adherent_id' => $adherent->id,
                'numero_adherent' => $adherent->numero_adherent,
                'created_by' => Auth::id()
            ]);

            return $adherent;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating adherent', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Update adherent
     */
    public function updateAdherent(Adherent $adherent, array $data): bool
    {
        DB::beginTransaction();
        
        try {
            $result = $this->adherentRepository->update($adherent, $data);

            // Update beneficiaires if provided
            if (isset($data['beneficiaires'])) {
                $this->updateBeneficiaires($adherent, $data['beneficiaires']);
            }

            DB::commit();

            if ($result) {
                Log::info('Adherent updated', [
                    'adherent_id' => $adherent->id,
                    'numero_adherent' => $adherent->numero_adherent,
                    'updated_by' => Auth::id()
                ]);
            }

            return $result;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating adherent', [
                'adherent_id' => $adherent->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Suspend adherent
     */
    public function suspendAdherent(Adherent $adherent, string $reason = null): bool
    {
        $data = [
            'statut' => Adherent::STATUS_SUSPENDU,
            'observations' => $reason ? "Suspendu: {$reason}" : 'Suspendu'
        ];

        $result = $this->adherentRepository->update($adherent, $data);

        if ($result) {
            Log::info('Adherent suspended', [
                'adherent_id' => $adherent->id,
                'reason' => $reason,
                'suspended_by' => Auth::id()
            ]);
        }

        return $result;
    }

    /**
     * Reactivate adherent
     */
    public function reactivateAdherent(Adherent $adherent): bool
    {
        $data = [
            'statut' => Adherent::STATUS_ACTIF,
            'is_active' => true
        ];

        $result = $this->adherentRepository->update($adherent, $data);

        if ($result) {
            Log::info('Adherent reactivated', [
                'adherent_id' => $adherent->id,
                'reactivated_by' => Auth::id()
            ]);
        }

        return $result;
    }

    /**
     * Upload document for adherent
     */
    public function uploadDocument(Adherent $adherent, UploadedFile $file, string $type, string $description = null): bool
    {
        try {
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs("adherents/{$adherent->id}/documents", $filename, 'public');

            $adherent->documents()->create([
                'type_document' => $type,
                'nom_document' => $description ?: $file->getClientOriginalName(),
                'chemin_fichier' => $path,
                'taille_fichier' => $file->getSize(),
                'type_mime' => $file->getMimeType(),
                'date_upload' => now(),
                'uploaded_by' => Auth::id(),
            ]);

            Log::info('Document uploaded for adherent', [
                'adherent_id' => $adherent->id,
                'document_type' => $type,
                'filename' => $filename,
                'uploaded_by' => Auth::id()
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Error uploading document', [
                'adherent_id' => $adherent->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get adherent statistics
     */
    public function getStatistics(): array
    {
        return $this->adherentRepository->getStatistics();
    }

    /**
     * Search adherents
     */
    public function searchAdherents(array $criteria, int $perPage = 15)
    {
        return $this->adherentRepository->advancedSearch($criteria, $perPage);
    }

    /**
     * Get retirement eligible adherents
     */
    public function getRetirementEligible()
    {
        return $this->adherentRepository->getRetirementEligible();
    }

    /**
     * Create beneficiaires for adherent
     */
    private function createBeneficiaires(Adherent $adherent, array $beneficiaires): void
    {
        foreach ($beneficiaires as $beneficiaireData) {
            $adherent->beneficiaires()->create($beneficiaireData);
        }
    }

    /**
     * Update beneficiaires for adherent
     */
    private function updateBeneficiaires(Adherent $adherent, array $beneficiaires): void
    {
        // Delete existing beneficiaires
        $adherent->beneficiaires()->delete();
        
        // Create new ones
        $this->createBeneficiaires($adherent, $beneficiaires);
    }

    /**
     * Export adherents data
     */
    public function exportAdherents(array $filters = []): array
    {
        $adherents = $this->adherentRepository->getPaginatedAdherents($filters, 1000);
        
        return $adherents->map(function ($adherent) {
            return [
                'Numéro Adhérent' => $adherent->numero_adherent,
                'Nom' => $adherent->nom,
                'Prénoms' => $adherent->prenoms,
                'Date de Naissance' => $adherent->date_naissance->format('d/m/Y'),
                'Sexe' => $adherent->sexe,
                'Téléphone' => $adherent->telephone,
                'Email' => $adherent->email,
                'Employeur' => $adherent->employeur,
                'Salaire de Base' => $adherent->salaire_base,
                'Date d\'Adhésion' => $adherent->date_adhesion->format('d/m/Y'),
                'Statut' => $adherent->status_label,
            ];
        })->toArray();
    }

    /**
     * Import adherents from array data
     */
    public function importAdherents(array $data): array
    {
        $results = [
            'success' => 0,
            'errors' => []
        ];

        foreach ($data as $index => $row) {
            try {
                $this->createAdherent($row);
                $results['success']++;
            } catch (\Exception $e) {
                $results['errors'][] = [
                    'row' => $index + 1,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }
}
