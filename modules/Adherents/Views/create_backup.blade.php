@extends('layouts.template')

@section('title', 'Nouvel Adhérent - CRFM')

@section('page-header')
@section('page-title', 'Nouvel Adhérent')
@section('page-description', 'Créer un nouveau dossier d\'adhérent')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ route('adherents.index') }}">Adhérents</a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Nouveau</a></li>
    </ul>
@endsection
@endsection

@section('content')
<form method="POST" action="{{ route('adherents.store') }}" class="needs-validation" novalidate>
    @csrf
    
    <div class="row">
        <!-- Informations personnelles -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-user text-c-blue me-2"></i>Informations personnelles</h5>
                </div>
                <div class="card-block">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="required">Nom</label>
                                <input type="text" name="nom" class="form-control @if($errors->has('nom')) is-invalid @endif" 
                                       value="{{ old('nom') }}" required>
                                @if($errors->has('nom'))
                                    <div class="invalid-feedback">{{ $errors->first('nom') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="required">Prénoms</label>
                                <input type="text" name="prenoms" class="form-control @if($errors->has('prenoms')) is-invalid @endif" 
                                       value="{{ old('prenoms') }}" required>
                                @if($errors->has('prenoms'))
                                    <div class="invalid-feedback">{{ $errors->first('prenoms') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="required">Date de naissance</label>
                                <input type="date" name="date_naissance" class="form-control @if($errors->has('date_naissance')) is-invalid @endif" 
                                       value="{{ old('date_naissance') }}" required>
                                @if($errors->has('date_naissance'))
                                    <div class="invalid-feedback">{{ $errors->first('date_naissance') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Lieu de naissance</label>
                                <input type="text" name="lieu_naissance" class="form-control @if($errors->has('lieu_naissance')) is-invalid @endif" 
                                       value="{{ old('lieu_naissance') }}">
                                @if($errors->has('lieu_naissance'))
                                    <div class="invalid-feedback">{{ $errors->first('lieu_naissance') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="required">Sexe</label>
                                <select name="sexe" class="form-control @if($errors->has('sexe')) is-invalid @endif" required>
                                    <option value="">Sélectionner</option>
                                    <option value="M" {{ old('sexe') === 'M' ? 'selected' : '' }}>Masculin</option>
                                    <option value="F" {{ old('sexe') === 'F' ? 'selected' : '' }}>Féminin</option>
                                </select>
                                @if($errors->has('sexe'))
                                    <div class="invalid-feedback">{{ $errors->first("FIELD_NAME") }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Situation matrimoniale</label>
                                <select name="situation_matrimoniale" class="form-control @if($errors->has('situation_matrimoniale')) is-invalid @endif">
                                    <option value="">Sélectionner</option>
                                    <option value="celibataire" {{ old('situation_matrimoniale') === 'celibataire' ? 'selected' : '' }}>Célibataire</option>
                                    <option value="marie" {{ old('situation_matrimoniale') === 'marie' ? 'selected' : '' }}>Marié(e)</option>
                                    <option value="divorce" {{ old('situation_matrimoniale') === 'divorce' ? 'selected' : '' }}>Divorcé(e)</option>
                                    <option value="veuf" {{ old('situation_matrimoniale') === 'veuf' ? 'selected' : '' }}>Veuf/Veuve</option>
                                </select>
                                @if($errors->has('situation_matrimoniale'))
                                    <div class="invalid-feedback">{{ $errors->first("FIELD_NAME") }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Nationalité</label>
                                <input type="text" name="nationalite" class="form-control @if($errors->has('nationalite')) is-invalid @endif" 
                                       value="{{ old('nationalite', 'Mayotteenne') }}">
                                @if($errors->has('nationalite'))
                                    <div class="invalid-feedback">{{ $errors->first("FIELD_NAME") }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Téléphone</label>
                                <input type="tel" name="telephone" class="form-control @if($errors->has('telephone')) is-invalid @endif" 
                                       value="{{ old('telephone') }}" placeholder="+262 XX XX XX XX">
                                @if($errors->has('telephone'))
                                    <div class="invalid-feedback">{{ $errors->first("FIELD_NAME") }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Email</label>
                                <input type="email" name="email" class="form-control @if($errors->has('email')) is-invalid @endif" 
                                       value="{{ old('email') }}">
                                @if($errors->has('email'))
                                    <div class="invalid-feedback">{{ $errors->first("FIELD_NAME") }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Adresse domicile</label>
                        <textarea name="adresse_domicile" class="form-control @if($errors->has('adresse_domicile')) is-invalid @endif" 
                                  rows="2">{{ old('adresse_domicile') }}</textarea>
                        @if($errors->has('adresse_domicile'))
                            <div class="invalid-feedback">{{ $errors->first("FIELD_NAME") }}</div>
                        @endif
                    </div>
                </div>
            </div>
            
            <!-- Informations professionnelles -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-briefcase text-c-green me-2"></i>Informations professionnelles</h5>
                </div>
                <div class="card-block">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="required">Profession</label>
                                <input type="text" name="profession" class="form-control @if($errors->has('profession')) is-invalid @endif" 
                                       value="{{ old('profession') }}" required>
                                @if($errors->has('profession'))
                                    <div class="invalid-feedback">{{ $errors->first("FIELD_NAME") }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="required">Employeur</label>
                                <input type="text" name="employeur" class="form-control @if($errors->has('employeur')) is-invalid @endif" 
                                       value="{{ old('employeur') }}" required>
                                @if($errors->has('employeur'))
                                    <div class="invalid-feedback">{{ $errors->first("FIELD_NAME") }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Date d'embauche</label>
                                <input type="date" name="date_embauche" class="form-control @if($errors->has('date_embauche')) is-invalid @endif" 
                                       value="{{ old('date_embauche') }}">
                                @if($errors->has('date_embauche'))
                                    <div class="invalid-feedback">{{ $errors->first("FIELD_NAME") }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Salaire de base (€)</label>
                                <input type="number" name="salaire_base" class="form-control @if($errors->has('salaire_base')) is-invalid @endif" 
                                       value="{{ old('salaire_base') }}" min="0" step="1000">
                                @if($errors->has('salaire_base'))
                                    <div class="invalid-feedback">{{ $errors->first("FIELD_NAME") }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Adresse professionnelle</label>
                        <textarea name="adresse_professionnelle" class="form-control @if($errors->has('adresse_professionnelle')) is-invalid @endif" 
                                  rows="2">{{ old('adresse_professionnelle') }}</textarea>
                        @if($errors->has('adresse_professionnelle'))
                            <div class="invalid-feedback">{{ $errors->first("FIELD_NAME") }}</div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Informations complémentaires -->
        <div class="col-md-4">
            <!-- Pièces d'identité -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-credit-card text-c-yellow me-2"></i>Pièces d'identité</h5>
                </div>
                <div class="card-block">
                    <div class="form-group">
                        <label>Numéro CNI</label>
                        <input type="text" name="numero_cni" class="form-control @if($errors->has('numero_cni')) is-invalid @endif" 
                               value="{{ old('numero_cni') }}">
                        @if($errors->has('numero_cni'))
                            <div class="invalid-feedback">{{ $errors->first("FIELD_NAME") }}</div>
                        @endif
                    </div>
                    
                    <div class="form-group">
                        <label>Date délivrance CNI</label>
                        <input type="date" name="date_delivrance_cni" class="form-control @if($errors->has('date_delivrance_cni')) is-invalid @endif" 
                               value="{{ old('date_delivrance_cni') }}">
                        @if($errors->has('date_delivrance_cni'))
                            <div class="invalid-feedback">{{ $errors->first("FIELD_NAME") }}</div>
                        @endif
                    </div>
                    
                    <div class="form-group">
                        <label>Lieu délivrance CNI</label>
                        <input type="text" name="lieu_delivrance_cni" class="form-control @if($errors->has('lieu_delivrance_cni')) is-invalid @endif" 
                               value="{{ old('lieu_delivrance_cni') }}">
                        @if($errors->has('lieu_delivrance_cni'))
                            <div class="invalid-feedback">{{ $errors->first("FIELD_NAME") }}</div>
                        @endif
                    </div>
                    
                    <div class="form-group">
                        <label>Numéro passeport</label>
                        <input type="text" name="numero_passeport" class="form-control @if($errors->has('numero_passeport')) is-invalid @endif" 
                               value="{{ old('numero_passeport') }}">
                        @if($errors->has('numero_passeport'))
                            <div class="invalid-feedback">{{ $errors->first("FIELD_NAME") }}</div>
                        @endif
                    </div>
                </div>
            </div>
            
            <!-- Contact d'urgence -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-phone text-c-red me-2"></i>Contact d'urgence</h5>
                </div>
                <div class="card-block">
                    <div class="form-group">
                        <label>Nom du contact</label>
                        <input type="text" name="contact_urgence_nom" class="form-control @if($errors->has('contact_urgence_nom')) is-invalid @endif" 
                               value="{{ old('contact_urgence_nom') }}">
                        @if($errors->has('contact_urgence_nom'))
                            <div class="invalid-feedback">{{ $errors->first("FIELD_NAME") }}</div>
                        @endif
                    </div>
                    
                    <div class="form-group">
                        <label>Téléphone du contact</label>
                        <input type="tel" name="contact_urgence_telephone" class="form-control @if($errors->has('contact_urgence_telephone')) is-invalid @endif" 
                               value="{{ old('contact_urgence_telephone') }}">
                        @if($errors->has('contact_urgence_telephone'))
                            <div class="invalid-feedback">{{ $errors->first("FIELD_NAME") }}</div>
                        @endif
                    </div>
                </div>
            </div>
            
            <!-- Informations d'adhésion -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-calendar text-c-purple me-2"></i>Adhésion</h5>
                </div>
                <div class="card-block">
                    <div class="form-group">
                        <label>Date d'adhésion</label>
                        <input type="date" name="date_adhesion" class="form-control @if($errors->has('date_adhesion')) is-invalid @endif" 
                               value="{{ old('date_adhesion', date('Y-m-d')) }}">
                        @if($errors->has('date_adhesion'))
                            <div class="invalid-feedback">{{ $errors->first("FIELD_NAME") }}</div>
                        @endif
                    </div>
                    
                    <div class="form-group">
                        <label>Observations</label>
                        <textarea name="observations" class="form-control @if($errors->has('observations')) is-invalid @endif" 
                                  rows="3">{{ old('observations') }}</textarea>
                        @if($errors->has('observations'))
                            <div class="invalid-feedback">{{ $errors->first("FIELD_NAME") }}</div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Boutons d'action -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-block text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="feather icon-save me-2"></i>Créer l'adhérent
                    </button>
                    <a href="{{ route('adherents.index') }}" class="btn btn-secondary btn-lg ms-2">
                        <i class="feather icon-x me-2"></i>Annuler
                    </a>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('styles')
<style>
.required::after {
    content: " *";
    color: red;
}
</style>
@endpush

@push('scripts')
<script>
// Validation du formulaire
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
@endpush
