@extends('layouts.template')

@section('title', 'Gestion des Adhérents - CRFM')

@section('page-header')
@section('page-title', 'Gestion des Adhérents')
@section('page-description', 'Liste et gestion des adhérents CRFM')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Adhérents</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <!-- Statistiques rapides -->
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-blue text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($statistics['total'] ?? 0) }}</h4>
                        <h6 class="text-white m-b-0">Total Adhérents</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-users f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-green text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($statistics['actifs'] ?? 0) }}</h4>
                        <h6 class="text-white m-b-0">Actifs</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-check-circle f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-yellow text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($statistics['retraites'] ?? 0) }}</h4>
                        <h6 class="text-white m-b-0">Retraités</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-award f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card text-white" style="background: linear-gradient(135deg, #4a148c 0%, #6a1b9a 100%) !important;">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="f-w-600" style="color: #ffffff !important; font-weight: 600; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">{{ number_format($statistics['eligibles_retraite'] ?? 0) }}</h4>
                        <h6 class="m-b-0" style="color: #ffffff !important; text-shadow: 1px 1px 3px rgba(0,0,0,0.5); font-weight: 500;">Éligibles retraite</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-clock f-28" style="color: #ffffff !important; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Filtres et recherche -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-filter text-c-blue me-2"></i>Filtres et recherche</h5>
                <div class="card-header-right">
                    <a href="{{ route('adherents.create') }}" class="btn btn-primary btn-sm">
                        <i class="feather icon-plus me-1"></i>Nouvel adhérent
                    </a>
                </div>
            </div>
            <div class="card-block">
                <form method="GET" action="{{ route('adherents.index') }}">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Recherche</label>
                                <input type="text" name="search" class="form-control" 
                                       placeholder="Nom, prénom, numéro..." 
                                       value="{{ $filters['search'] ?? '' }}">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Statut</label>
                                <select name="statut" class="form-control">
                                    <option value="">Tous</option>
                                    <option value="actif" {{ ($filters['statut'] ?? '') === 'actif' ? 'selected' : '' }}>Actif</option>
                                    <option value="suspendu" {{ ($filters['statut'] ?? '') === 'suspendu' ? 'selected' : '' }}>Suspendu</option>
                                    <option value="radie" {{ ($filters['statut'] ?? '') === 'radie' ? 'selected' : '' }}>Radié</option>
                                    <option value="retraite" {{ ($filters['statut'] ?? '') === 'retraite' ? 'selected' : '' }}>Retraité</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Sexe</label>
                                <select name="sexe" class="form-control">
                                    <option value="">Tous</option>
                                    <option value="M" {{ ($filters['sexe'] ?? '') === 'M' ? 'selected' : '' }}>Masculin</option>
                                    <option value="F" {{ ($filters['sexe'] ?? '') === 'F' ? 'selected' : '' }}>Féminin</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Employeur</label>
                                <input type="text" name="employeur" class="form-control" 
                                       placeholder="Nom de l'employeur" 
                                       value="{{ $filters['employeur'] ?? '' }}">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div class="btn-group btn-block">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="feather icon-search"></i>
                                    </button>
                                    <a href="{{ route('adherents.index') }}" class="btn btn-secondary">
                                        <i class="feather icon-refresh-cw"></i>
                                    </a>
                                    <a href="{{ route('adherents.export', $filters) }}" class="btn btn-success">
                                        <i class="feather icon-download"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Liste des adhérents -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-list text-c-blue me-2"></i>Liste des adhérents</h5>
                <div class="card-header-right">
                    <span class="badge bg-info">{{ $adherents->total() }} résultat(s)</span>
                </div>
            </div>
            <div class="card-block">
                @if($adherents->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>N° Adhérent</th>
                                <th>Nom & Prénoms</th>
                                <th>Date naissance</th>
                                <th>Employeur</th>
                                <th>Statut</th>
                                <th>Date adhésion</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($adherents as $adherent)
                            <tr>
                                <td>
                                    <strong>{{ $adherent->numero_adherent }}</strong>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ $adherent->full_name }}</strong>
                                        <br><small class="text-muted">{{ $adherent->age }} ans</small>
                                    </div>
                                </td>
                                <td>{{ $adherent->date_naissance?->format('d/m/Y') }}</td>
                                <td>
                                    <small>{{ Str::limit($adherent->employeur, 30) }}</small>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $adherent->status_color }}">
                                        {{ $adherent->status_label }}
                                    </span>
                                </td>
                                <td>{{ $adherent->date_adhesion?->format('d/m/Y') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('adherents.show', $adherent) }}" 
                                           class="btn btn-primary" title="Voir">
                                            <i class="feather icon-eye"></i>
                                        </a>
                                        <a href="{{ route('adherents.edit', $adherent) }}" 
                                           class="btn btn-warning" title="Modifier">
                                            <i class="feather icon-edit"></i>
                                        </a>
                                        @if($adherent->statut === 'actif')
                                        <button class="btn btn-secondary" 
                                                onclick="suspendAdherent({{ $adherent->id }})" 
                                                title="Suspendre">
                                            <i class="feather icon-pause"></i>
                                        </button>
                                        @elseif($adherent->statut === 'suspendu')
                                        <form method="POST" action="{{ route('adherents.reactivate', $adherent) }}" style="display: inline;">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit" class="btn btn-success" title="Réactiver">
                                                <i class="feather icon-play"></i>
                                            </button>
                                        </form>
                                        @endif
                                        <button class="btn btn-danger" 
                                                onclick="deleteAdherent({{ $adherent->id }})" 
                                                title="Supprimer">
                                            <i class="feather icon-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="row">
                    <div class="col-sm-12 col-md-5">
                        <div class="dataTables_info">
                            Affichage de {{ $adherents->firstItem() }} à {{ $adherents->lastItem() }} 
                            sur {{ $adherents->total() }} entrées
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-7">
                        {{ $adherents->appends($filters)->links() }}
                    </div>
                </div>
                @else
                <div class="text-center py-4">
                    <i class="feather icon-users f-40 text-muted"></i>
                    <h5 class="mt-3">Aucun adhérent trouvé</h5>
                    <p class="text-muted">Aucun adhérent ne correspond aux critères de recherche.</p>
                    <a href="{{ route('adherents.create') }}" class="btn btn-primary">
                        <i class="feather icon-plus me-2"></i>Créer le premier adhérent
                    </a>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Modal de suspension -->
<div class="modal fade" id="suspendModal" tabindex="-1" aria-labelledby="suspendModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="suspendModalLabel">Suspendre l'adhérent</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="suspendForm" method="POST">
                @csrf
                @method('PATCH')
                <div class="modal-body">
                    <div class="form-group mb-3">
                        <label class="form-label">Motif de suspension (optionnel)</label>
                        <textarea name="reason" class="form-control" rows="3"
                                  placeholder="Précisez le motif de la suspension..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-warning">Suspendre</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Debug: Vérifier que le script se charge
console.log('Script adherents chargé');

function suspendAdherent(adherentId) {
    console.log('suspendAdherent appelé avec ID:', adherentId);

    const form = document.getElementById('suspendForm');
    if (!form) {
        console.error('Formulaire suspendForm non trouvé');
        return;
    }

    form.action = `/adherents/${adherentId}/suspend`;
    console.log('Action du formulaire définie:', form.action);

    // Bootstrap 5 - Utilisation de l'API native
    const modalElement = document.getElementById('suspendModal');
    if (!modalElement) {
        console.error('Modal suspendModal non trouvé');
        return;
    }

    // Vérifier si Bootstrap est disponible
    if (typeof bootstrap === 'undefined') {
        console.error('Bootstrap non disponible');
        alert('Erreur: Bootstrap non chargé');
        return;
    }

    const modal = new bootstrap.Modal(modalElement);
    modal.show();
}

function deleteAdherent(adherentId) {
    console.log('deleteAdherent appelé avec ID:', adherentId);

    if (confirm('Êtes-vous sûr de vouloir supprimer cet adhérent ? Cette action est irréversible.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/adherents/${adherentId}`;

        // Ajouter le token CSRF
        const csrfTokenMeta = document.querySelector('meta[name="csrf-token"]');
        if (!csrfTokenMeta) {
            console.error('Token CSRF non trouvé');
            alert('Erreur: Token CSRF manquant');
            return;
        }

        const csrfToken = csrfTokenMeta.getAttribute('content');
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        // Ajouter la méthode DELETE
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);

        console.log('Formulaire créé, soumission...');
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush

@push('styles')
<style>
/* Amélioration de la visibilité du texte sur la carte "Éligibles retraite" */
.card[style*="4a148c"] {
    background: linear-gradient(135deg, #4a148c 0%, #6a1b9a 100%) !important;
    border: none;
    box-shadow: 0 4px 20px rgba(74, 20, 140, 0.3);
}

.card[style*="4a148c"] h4,
.card[style*="4a148c"] h6,
.card[style*="4a148c"] .feather {
    color: #ffffff !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.6);
    font-weight: 600;
}

.card[style*="4a148c"] h6 {
    font-weight: 500 !important;
    letter-spacing: 0.5px;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.6);
}

/* Amélioration générale des cartes statistiques */
.card[style*="4a148c"]:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(74, 20, 140, 0.4);
    transition: all 0.3s ease;
}

/* Fallback pour toutes les cartes violettes */
.bg-c-purple,
.card.bg-c-purple {
    background: linear-gradient(135deg, #4a148c 0%, #6a1b9a 100%) !important;
}

.bg-c-purple .text-white,
.bg-c-purple h4,
.bg-c-purple h6,
.bg-c-purple .feather {
    color: #ffffff !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.6);
}

/* Assurer la visibilité sur tous les navigateurs */
.card .text-white {
    color: #ffffff !important;
}

.card h4.text-white,
.card h6.text-white {
    color: #ffffff !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}
</style>
@endpush
