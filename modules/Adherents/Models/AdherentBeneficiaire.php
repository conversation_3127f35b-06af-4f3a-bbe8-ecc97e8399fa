<?php

namespace Modules\Adherents\Models;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AdherentBeneficiaire extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'adherent_id',
        'nom',
        'prenoms',
        'date_naissance',
        'lieu_naissance',
        'sexe',
        'lien_parente',
        'telephone',
        'adresse',
        'pourcentage_benefice',
        'is_active'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'date_naissance' => 'date',
        'pourcentage_benefice' => 'decimal:2',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Relationship constants
     */
    const LIEN_CONJOINT = 'conjoint';
    const LIEN_ENFANT = 'enfant';
    const LIEN_PARENT = 'parent';
    const LIEN_FRERE_SOEUR = 'frere_soeur';
    const LIEN_AUTRE = 'autre';

    /**
     * Get adherent that owns this beneficiaire
     */
    public function adherent(): BelongsTo
    {
        return $this->belongsTo(Adherent::class);
    }

    /**
     * Get full name attribute
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->nom} {$this->prenoms}";
    }

    /**
     * Get age attribute
     */
    public function getAgeAttribute(): int
    {
        return $this->date_naissance ? $this->date_naissance->age : 0;
    }

    /**
     * Get relationship label
     */
    public function getLienLabelAttribute(): string
    {
        return match($this->lien_parente) {
            self::LIEN_CONJOINT => 'Conjoint(e)',
            self::LIEN_ENFANT => 'Enfant',
            self::LIEN_PARENT => 'Parent',
            self::LIEN_FRERE_SOEUR => 'Frère/Sœur',
            self::LIEN_AUTRE => 'Autre',
            default => 'Non défini'
        };
    }

    /**
     * Scope for active beneficiaires
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope by relationship
     */
    public function scopeByRelationship($query, string $relationship)
    {
        return $query->where('lien_parente', $relationship);
    }
}
