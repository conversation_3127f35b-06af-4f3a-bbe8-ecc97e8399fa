<?php

namespace Modules\Adherents\Models;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Adherent extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'numero_adherent',
        'nom',
        'prenoms',
        'date_naissance',
        'lieu_naissance',
        'sexe',
        'situation_matrimoniale',
        'nationalite',
        'profession',
        'employeur',
        'date_embauche',
        'salaire_base',
        'telephone',
        'email',
        'adresse_domicile',
        'adresse_professionnelle',
        'contact_urgence_nom',
        'contact_urgence_telephone',
        'numero_cni',
        'date_delivrance_cni',
        'lieu_delivrance_cni',
        'numero_passeport',
        'date_adhesion',
        'statut',
        'observations',
        'validated_at',
        'validated_by',
        'is_active'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'date_naissance' => 'date',
        'date_embauche' => 'date',
        'date_delivrance_cni' => 'date',
        'date_adhesion' => 'date',
        'validated_at' => 'datetime',
        'salaire_base' => 'decimal:2',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Searchable fields
     */
    protected array $searchable = [
        'numero_adherent',
        'nom',
        'prenoms',
        'telephone',
        'email',
        'numero_cni',
        'employeur'
    ];

    /**
     * Status constants
     */
    const STATUS_ACTIF = 'actif';
    const STATUS_SUSPENDU = 'suspendu';
    const STATUS_RADIE = 'radie';
    const STATUS_RETRAITE = 'retraite';

    /**
     * Gender constants
     */
    const SEXE_MASCULIN = 'M';
    const SEXE_FEMININ = 'F';

    /**
     * Marital status constants
     */
    const SITUATION_CELIBATAIRE = 'celibataire';
    const SITUATION_MARIE = 'marie';
    const SITUATION_DIVORCE = 'divorce';
    const SITUATION_VEUF = 'veuf';

    /**
     * Get cotisations for this adherent
     */
    public function cotisations(): HasMany
    {
        return $this->hasMany(\Modules\Cotisations\Models\Cotisation::class);
    }

    /**
     * Get pension dossier for this adherent
     */
    public function dossierPension(): HasOne
    {
        return $this->hasOne(\Modules\Pensions\Models\DossierPension::class);
    }

    /**
     * Get the user who validated this adherent
     */
    public function validator()
    {
        return $this->belongsTo(\App\Models\User::class, 'validated_by');
    }

    /**
     * Get documents for this adherent
     */
    public function documents(): HasMany
    {
        return $this->hasMany(AdherentDocument::class);
    }

    /**
     * Get beneficiaires for this adherent
     */
    public function beneficiaires(): HasMany
    {
        return $this->hasMany(AdherentBeneficiaire::class);
    }

    /**
     * Get full name attribute
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->nom} {$this->prenoms}";
    }

    /**
     * Get age attribute
     */
    public function getAgeAttribute(): int
    {
        return $this->date_naissance ? $this->date_naissance->age : 0;
    }

    /**
     * Get years of service attribute
     */
    public function getYearsOfServiceAttribute(): int
    {
        return $this->date_embauche ? $this->date_embauche->diffInYears(now()) : 0;
    }

    /**
     * Get formatted salary attribute
     */
    public function getFormattedSalaireAttribute(): string
    {
        return number_format($this->salaire_base, 0, ',', ' ') . ' €';
    }

    /**
     * Scope for active adherents
     */
    public function scopeActive($query)
    {
        return $query->where('statut', self::STATUS_ACTIF)->where('is_active', true);
    }

    /**
     * Scope by status
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('statut', $status);
    }

    /**
     * Scope by gender
     */
    public function scopeByGender($query, string $gender)
    {
        return $query->where('sexe', $gender);
    }

    /**
     * Scope by employer
     */
    public function scopeByEmployer($query, string $employer)
    {
        return $query->where('employeur', 'LIKE', "%{$employer}%");
    }

    /**
     * Scope for retirement eligible (assuming 60 years)
     */
    public function scopeRetirementEligible($query)
    {
        $retirementDate = now()->subYears(60);
        return $query->where('date_naissance', '<=', $retirementDate);
    }



    /**
     * Check if adherent is retirement eligible
     */
    public function isRetirementEligible(): bool
    {
        return $this->age >= 60;
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->statut) {
            self::STATUS_ACTIF => 'Actif',
            self::STATUS_SUSPENDU => 'Suspendu',
            self::STATUS_RADIE => 'Radié',
            self::STATUS_RETRAITE => 'Retraité',
            default => 'Inconnu'
        };
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->statut) {
            self::STATUS_ACTIF => 'success',
            self::STATUS_SUSPENDU => 'warning',
            self::STATUS_RADIE => 'danger',
            self::STATUS_RETRAITE => 'info',
            default => 'secondary'
        };
    }

    /**
     * Get the route key name for Laravel.
     * Override BaseModel to use ID instead of UUID for adherents
     */
    public function getRouteKeyName(): string
    {
        return 'id';
    }
}
