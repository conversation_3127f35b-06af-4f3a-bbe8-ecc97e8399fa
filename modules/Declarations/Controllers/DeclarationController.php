<?php

namespace Modules\Declarations\Controllers;

use App\Http\Controllers\BaseController;
use Modules\Declarations\Models\Declaration;
use Modules\Declarations\Services\DeclarationService;
use Modules\Declarations\Repositories\DeclarationRepository;
use Modules\Declarations\Requests\CreateDeclarationRequest;
use Modules\Declarations\Requests\UpdateDeclarationRequest;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Exception;

class DeclarationController extends BaseController
{
    protected DeclarationService $declarationService;
    protected DeclarationRepository $declarationRepository;

    public function __construct(
        DeclarationService $declarationService,
        DeclarationRepository $declarationRepository
    ) {
        $this->declarationService = $declarationService;
        $this->declarationRepository = $declarationRepository;
    }

    /**
     * Afficher la liste des déclarations
     */
    public function index(Request $request): View
    {
        $filters = $request->only(['employeur', 'statut', 'annee', 'mois', 'search', 'date_debut', 'date_fin']);
        $perPage = $request->get('per_page', 15);

        $declarations = $this->declarationRepository->getAllWithFilters($filters, $perPage);
        $statistics = $this->declarationService->getStatistics();
        $topEmployeurs = $this->declarationRepository->getTopEmployeurs();
        $anneesDisponibles = $this->declarationRepository->getAnneesDisponibles();
        $employeursUniques = $this->declarationRepository->getEmployeursUniques();

        return view('declarations::index', compact(
            'declarations',
            'statistics',
            'topEmployeurs',
            'anneesDisponibles',
            'employeursUniques',
            'filters'
        ));
    }

    /**
     * Afficher le formulaire de création
     */
    public function create(): View
    {
        $employeursUniques = $this->declarationRepository->getEmployeursUniques();
        
        return view('declarations::create', compact('employeursUniques'));
    }

    /**
     * Enregistrer une nouvelle déclaration
     */
    public function store(CreateDeclarationRequest $request): RedirectResponse
    {
        try {
            $declaration = $this->declarationService->createDeclaration(
                $request->validated(),
                auth()->user()
            );

            return $this->successRedirect(
                'declarations.show',
                'Déclaration créée avec succès !',
                ['declaration' => $declaration]
            );

        } catch (Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Erreur lors de la création : ' . $e->getMessage());
        }
    }

    /**
     * Afficher une déclaration
     */
    public function show(Declaration $declaration): View
    {
        $declaration = $this->declarationRepository->findWithRelations($declaration->id);
        
        if (!$declaration) {
            abort(404, 'Déclaration non trouvée');
        }

        return view('declarations::show', compact('declaration'));
    }

    /**
     * Afficher le formulaire de modification
     */
    public function edit(Declaration $declaration): View
    {
        if (!$declaration->peutEtreModifiee()) {
            return back()->with('error', 'Cette déclaration ne peut plus être modifiée.');
        }

        $employeursUniques = $this->declarationRepository->getEmployeursUniques();
        
        return view('declarations::edit', compact('declaration', 'employeursUniques'));
    }

    /**
     * Mettre à jour une déclaration
     */
    public function update(UpdateDeclarationRequest $request, Declaration $declaration): RedirectResponse
    {
        try {
            $declaration = $this->declarationService->updateDeclaration(
                $declaration,
                $request->validated(),
                auth()->user()
            );

            return $this->successRedirect(
                'declarations.show',
                'Déclaration mise à jour avec succès !',
                ['declaration' => $declaration]
            );

        } catch (Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Erreur lors de la mise à jour : ' . $e->getMessage());
        }
    }

    /**
     * Supprimer une déclaration
     */
    public function destroy(Declaration $declaration): RedirectResponse
    {
        try {
            $this->declarationService->deleteDeclaration($declaration, auth()->user());

            return $this->successRedirect(
                'declarations.index',
                'Déclaration supprimée avec succès !'
            );

        } catch (Exception $e) {
            return back()->with('error', 'Erreur lors de la suppression : ' . $e->getMessage());
        }
    }

    /**
     * Marquer une déclaration en traitement
     */
    public function marquerEnTraitement(Declaration $declaration): RedirectResponse
    {
        try {
            $this->declarationService->marquerEnTraitement($declaration, auth()->user());

            return back()->with('success', 'Déclaration mise en traitement avec succès !');

        } catch (Exception $e) {
            return back()->with('error', 'Erreur : ' . $e->getMessage());
        }
    }

    /**
     * Valider une déclaration
     */
    public function valider(Request $request, Declaration $declaration): RedirectResponse
    {
        $request->validate([
            'observations' => 'nullable|string|max:1000'
        ]);

        try {
            $this->declarationService->validerDeclaration(
                $declaration,
                auth()->user(),
                $request->get('observations')
            );

            return back()->with('success', 'Déclaration validée avec succès !');

        } catch (Exception $e) {
            return back()->with('error', 'Erreur : ' . $e->getMessage());
        }
    }

    /**
     * Rejeter une déclaration
     */
    public function rejeter(Request $request, Declaration $declaration): RedirectResponse
    {
        $request->validate([
            'motif_rejet' => 'required|string|max:1000'
        ]);

        try {
            $this->declarationService->rejeterDeclaration(
                $declaration,
                auth()->user(),
                $request->get('motif_rejet')
            );

            return back()->with('success', 'Déclaration rejetée avec succès !');

        } catch (Exception $e) {
            return back()->with('error', 'Erreur : ' . $e->getMessage());
        }
    }

    /**
     * Export des déclarations
     */
    public function export(Request $request): JsonResponse
    {
        // TODO: Implémenter l'export CSV/Excel
        return response()->json([
            'success' => true,
            'message' => 'Export en cours de développement'
        ]);
    }

    /**
     * API - Statistiques pour le dashboard
     */
    public function apiStatistics(): JsonResponse
    {
        try {
            $statistics = $this->declarationService->getStatistics();
            
            return response()->json([
                'success' => true,
                'data' => $statistics
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des statistiques'
            ], 500);
        }
    }

    /**
     * API - Recherche de déclarations
     */
    public function apiSearch(Request $request): JsonResponse
    {
        $request->validate([
            'term' => 'required|string|min:2'
        ]);

        try {
            $declarations = $this->declarationRepository->search($request->get('term'));
            
            return response()->json([
                'success' => true,
                'data' => $declarations->map(function ($declaration) {
                    return [
                        'id' => $declaration->id,
                        'numero_declaration' => $declaration->numero_declaration,
                        'employeur' => $declaration->employeur,
                        'periode' => $declaration->periode_formatted,
                        'statut' => $declaration->statut_label,
                        'montant' => $declaration->montant_total_formate
                    ];
                })
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la recherche'
            ], 500);
        }
    }

    /**
     * API - Évolution des déclarations
     */
    public function apiEvolution(Request $request): JsonResponse
    {
        $annee = $request->get('annee', date('Y'));

        try {
            $evolution = $this->declarationRepository->getEvolutionParMois($annee);
            
            return response()->json([
                'success' => true,
                'data' => $evolution
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération de l\'évolution'
            ], 500);
        }
    }
}
