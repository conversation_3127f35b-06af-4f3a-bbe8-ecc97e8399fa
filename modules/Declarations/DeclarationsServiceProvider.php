<?php

namespace Modules\Declarations;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;

class DeclarationsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Enregistrer les services
        $this->app->bind(
            \Modules\Declarations\Repositories\DeclarationRepository::class,
            \Modules\Declarations\Repositories\DeclarationRepository::class
        );

        $this->app->bind(
            \Modules\Declarations\Services\DeclarationService::class,
            \Modules\Declarations\Services\DeclarationService::class
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Charger les routes
        $this->loadRoutesFrom(__DIR__ . '/routes.php');

        // Charger les vues
        $this->loadViewsFrom(__DIR__ . '/Views', 'declarations');

        // Publier les assets si nécessaire
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__ . '/Views' => resource_path('views/vendor/declarations'),
            ], 'declarations-views');
        }
    }
}
