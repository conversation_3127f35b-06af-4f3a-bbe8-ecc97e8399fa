<?php

return [
    'name' => 'Declarations',
    'description' => 'Module de gestion des déclarations de cotisations',
    'version' => '1.0.0',
    
    // Configuration des fichiers
    'files' => [
        'max_size' => [
            'declaration' => 10240, // 10MB en KB
            'justificatifs' => 20480, // 20MB en KB
        ],
        'allowed_types' => [
            'declaration' => ['pdf', 'doc', 'docx', 'xls', 'xlsx'],
            'justificatifs' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'zip'],
        ],
        'storage_path' => 'declarations',
    ],
    
    // Configuration des statuts
    'statuts' => [
        'en_attente' => [
            'label' => 'En attente',
            'color' => 'info',
            'icon' => 'clock',
        ],
        'en_traitement' => [
            'label' => 'En traitement',
            'color' => 'warning',
            'icon' => 'play',
        ],
        'validee' => [
            'label' => 'Validée',
            'color' => 'success',
            'icon' => 'check-circle',
        ],
        'rejetee' => [
            'label' => 'Rejetée',
            'color' => 'danger',
            'icon' => 'x-circle',
        ],
        'incomplete' => [
            'label' => 'Incomplète',
            'color' => 'secondary',
            'icon' => 'alert-circle',
        ],
    ],
    
    // Configuration de la pagination
    'pagination' => [
        'per_page' => 15,
        'per_page_options' => [10, 15, 25, 50],
    ],
    
    // Configuration des exports
    'export' => [
        'formats' => ['csv', 'xlsx'],
        'max_records' => 10000,
    ],
    
    // Configuration des notifications
    'notifications' => [
        'enabled' => true,
        'channels' => ['mail', 'database'],
        'events' => [
            'declaration_created',
            'declaration_validated',
            'declaration_rejected',
        ],
    ],
];
