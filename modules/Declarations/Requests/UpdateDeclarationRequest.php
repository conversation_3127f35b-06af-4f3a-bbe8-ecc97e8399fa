<?php

namespace Modules\Declarations\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Modules\Declarations\Models\Declaration;

class UpdateDeclarationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // Informations de base
            'employeur' => 'required|string|max:255',
            'siret_employeur' => 'nullable|string|max:14|regex:/^[0-9]{14}$/',
            'adresse_employeur' => 'nullable|string|max:500',
            
            // Période
            'annee' => 'required|integer|min:2020|max:' . (date('Y') + 1),
            'mois' => 'required|integer|min:1|max:12',
            
            // Données financières
            'nombre_employes' => 'required|integer|min:0|max:999999',
            'montant_total' => 'required|numeric|min:0|max:999999999.99',
            'montant_cotisations_salariales' => 'nullable|numeric|min:0|max:999999999.99',
            'montant_cotisations_patronales' => 'nullable|numeric|min:0|max:999999999.99',
            
            // Dates
            'date_reception' => 'nullable|date|before_or_equal:today',
            'date_echeance' => 'nullable|date|after:date_reception',
            
            // Fichiers (optionnels en modification)
            'fichier_declaration' => 'nullable|file|mimes:pdf,doc,docx,xls,xlsx|max:10240', // 10MB
            'fichier_justificatifs' => 'nullable|file|mimes:pdf,doc,docx,xls,xlsx,zip|max:20480', // 20MB
            
            // Autres
            'observations' => 'nullable|string|max:1000',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'employeur.required' => 'Le nom de l\'employeur est obligatoire.',
            'employeur.max' => 'Le nom de l\'employeur ne peut pas dépasser 255 caractères.',
            
            'siret_employeur.regex' => 'Le SIRET doit contenir exactement 14 chiffres.',
            
            'annee.required' => 'L\'année est obligatoire.',
            'annee.integer' => 'L\'année doit être un nombre entier.',
            'annee.min' => 'L\'année ne peut pas être antérieure à 2020.',
            'annee.max' => 'L\'année ne peut pas être supérieure à ' . (date('Y') + 1) . '.',
            
            'mois.required' => 'Le mois est obligatoire.',
            'mois.integer' => 'Le mois doit être un nombre entier.',
            'mois.min' => 'Le mois doit être compris entre 1 et 12.',
            'mois.max' => 'Le mois doit être compris entre 1 et 12.',
            
            'nombre_employes.required' => 'Le nombre d\'employés est obligatoire.',
            'nombre_employes.integer' => 'Le nombre d\'employés doit être un nombre entier.',
            'nombre_employes.min' => 'Le nombre d\'employés ne peut pas être négatif.',
            'nombre_employes.max' => 'Le nombre d\'employés ne peut pas dépasser 999 999.',
            
            'montant_total.required' => 'Le montant total est obligatoire.',
            'montant_total.numeric' => 'Le montant total doit être un nombre.',
            'montant_total.min' => 'Le montant total ne peut pas être négatif.',
            'montant_total.max' => 'Le montant total ne peut pas dépasser 999 999 999,99 €.',
            
            'montant_cotisations_salariales.numeric' => 'Le montant des cotisations salariales doit être un nombre.',
            'montant_cotisations_salariales.min' => 'Le montant des cotisations salariales ne peut pas être négatif.',
            'montant_cotisations_salariales.max' => 'Le montant des cotisations salariales ne peut pas dépasser 999 999 999,99 €.',
            
            'montant_cotisations_patronales.numeric' => 'Le montant des cotisations patronales doit être un nombre.',
            'montant_cotisations_patronales.min' => 'Le montant des cotisations patronales ne peut pas être négatif.',
            'montant_cotisations_patronales.max' => 'Le montant des cotisations patronales ne peut pas dépasser 999 999 999,99 €.',
            
            'date_reception.date' => 'La date de réception doit être une date valide.',
            'date_reception.before_or_equal' => 'La date de réception ne peut pas être dans le futur.',
            
            'date_echeance.date' => 'La date d\'échéance doit être une date valide.',
            'date_echeance.after' => 'La date d\'échéance doit être postérieure à la date de réception.',
            
            'fichier_declaration.file' => 'Le fichier de déclaration doit être un fichier valide.',
            'fichier_declaration.mimes' => 'Le fichier de déclaration doit être au format PDF, DOC, DOCX, XLS ou XLSX.',
            'fichier_declaration.max' => 'Le fichier de déclaration ne peut pas dépasser 10 MB.',
            
            'fichier_justificatifs.file' => 'Le fichier de justificatifs doit être un fichier valide.',
            'fichier_justificatifs.mimes' => 'Le fichier de justificatifs doit être au format PDF, DOC, DOCX, XLS, XLSX ou ZIP.',
            'fichier_justificatifs.max' => 'Le fichier de justificatifs ne peut pas dépasser 20 MB.',
            
            'observations.max' => 'Les observations ne peuvent pas dépasser 1000 caractères.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'employeur' => 'employeur',
            'siret_employeur' => 'SIRET',
            'adresse_employeur' => 'adresse de l\'employeur',
            'annee' => 'année',
            'mois' => 'mois',
            'nombre_employes' => 'nombre d\'employés',
            'montant_total' => 'montant total',
            'montant_cotisations_salariales' => 'cotisations salariales',
            'montant_cotisations_patronales' => 'cotisations patronales',
            'date_reception' => 'date de réception',
            'date_echeance' => 'date d\'échéance',
            'fichier_declaration' => 'fichier de déclaration',
            'fichier_justificatifs' => 'fichier de justificatifs',
            'observations' => 'observations',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Vérifier que la somme des cotisations ne dépasse pas le montant total
            if ($this->montant_cotisations_salariales && $this->montant_cotisations_patronales && $this->montant_total) {
                $sommeCotisations = $this->montant_cotisations_salariales + $this->montant_cotisations_patronales;
                if ($sommeCotisations > $this->montant_total) {
                    $validator->errors()->add('montant_total', 'Le montant total ne peut pas être inférieur à la somme des cotisations salariales et patronales.');
                }
            }

            // Vérifier l'unicité de la déclaration pour la période (exclure la déclaration actuelle)
            $declarationId = $this->route('declaration')->id;
            $exists = Declaration::where('employeur', $this->employeur)
                ->where('annee', $this->annee)
                ->where('mois', $this->mois)
                ->where('id', '!=', $declarationId)
                ->exists();

            if ($exists) {
                $validator->errors()->add('periode', 'Une autre déclaration existe déjà pour cet employeur et cette période.');
            }
        });
    }
}
