<?php

namespace Modules\Declarations\Models;

use App\Models\BaseModel;
use App\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Declaration extends BaseModel
{
    use SoftDeletes;

    protected $fillable = [
        'uuid',
        'numero_declaration',
        'employeur',
        'siret_employeur',
        'adresse_employeur',
        'annee',
        'mois',
        'periode',
        'nombre_employes',
        'montant_total',
        'montant_cotisations_salariales',
        'montant_cotisations_patronales',
        'date_reception',
        'date_echeance',
        'date_traitement',
        'date_validation',
        'statut',
        'motif_rejet',
        'observations',
        'fichier_declaration',
        'fichier_justificatifs',
        'documents_annexes',
        'user_created_id',
        'user_treated_id',
        'user_validated_id',
        'is_rectificative',
        'declaration_originale_id',
        'version'
    ];

    protected $casts = [
        'date_reception' => 'date',
        'date_echeance' => 'date',
        'date_traitement' => 'date',
        'date_validation' => 'date',
        'montant_total' => 'decimal:2',
        'montant_cotisations_salariales' => 'decimal:2',
        'montant_cotisations_patronales' => 'decimal:2',
        'documents_annexes' => 'array',
        'is_rectificative' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    // Constantes pour les statuts
    const STATUT_EN_ATTENTE = 'en_attente';
    const STATUT_EN_TRAITEMENT = 'en_traitement';
    const STATUT_VALIDEE = 'validee';
    const STATUT_REJETEE = 'rejetee';
    const STATUT_INCOMPLETE = 'incomplete';

    const STATUTS = [
        self::STATUT_EN_ATTENTE => 'En attente',
        self::STATUT_EN_TRAITEMENT => 'En traitement',
        self::STATUT_VALIDEE => 'Validée',
        self::STATUT_REJETEE => 'Rejetée',
        self::STATUT_INCOMPLETE => 'Incomplète',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($declaration) {
            if (empty($declaration->uuid)) {
                $declaration->uuid = Str::uuid();
            }
            // Le numéro de déclaration sera généré par le service pour éviter les conflits
            if (empty($declaration->periode) && $declaration->annee && $declaration->mois) {
                $declaration->periode = self::formatPeriode($declaration->annee, $declaration->mois);
            }
        });
    }

    /**
     * Relations
     */
    public function userCreated(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_created_id');
    }

    public function userTreated(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_treated_id');
    }

    public function userValidated(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_validated_id');
    }

    public function declarationOriginale(): BelongsTo
    {
        return $this->belongsTo(Declaration::class, 'declaration_originale_id');
    }

    public function declarationsRectificatives(): HasMany
    {
        return $this->hasMany(Declaration::class, 'declaration_originale_id');
    }

    /**
     * Accessors
     */
    public function getStatutLabelAttribute(): string
    {
        return self::STATUTS[$this->statut] ?? 'Inconnu';
    }

    public function getStatutColorAttribute(): string
    {
        return match($this->statut) {
            self::STATUT_EN_ATTENTE => 'info',
            self::STATUT_EN_TRAITEMENT => 'warning',
            self::STATUT_VALIDEE => 'success',
            self::STATUT_REJETEE => 'danger',
            self::STATUT_INCOMPLETE => 'secondary',
            default => 'secondary'
        };
    }

    public function getMontantTotalFormateAttribute(): string
    {
        return number_format($this->montant_total, 0, ',', ' ') . ' €';
    }

    public function getPeriodeFormattedAttribute(): string
    {
        $mois = [
            1 => 'Janvier', 2 => 'Février', 3 => 'Mars', 4 => 'Avril',
            5 => 'Mai', 6 => 'Juin', 7 => 'Juillet', 8 => 'Août',
            9 => 'Septembre', 10 => 'Octobre', 11 => 'Novembre', 12 => 'Décembre'
        ];
        
        return ($mois[$this->mois] ?? 'Mois inconnu') . ' ' . $this->annee;
    }

    /**
     * Scopes
     */
    public function scopeParStatut($query, $statut)
    {
        return $query->where('statut', $statut);
    }

    public function scopeParEmployeur($query, $employeur)
    {
        return $query->where('employeur', 'like', '%' . $employeur . '%');
    }

    public function scopeParPeriode($query, $annee, $mois = null)
    {
        $query->where('annee', $annee);
        if ($mois) {
            $query->where('mois', $mois);
        }
        return $query;
    }

    public function scopeRecentes($query, $jours = 30)
    {
        return $query->where('date_reception', '>=', now()->subDays($jours));
    }

    /**
     * Méthodes métier
     */
    public function peutEtreModifiee(): bool
    {
        return in_array($this->statut, [self::STATUT_EN_ATTENTE, self::STATUT_INCOMPLETE]);
    }

    public function peutEtreValidee(): bool
    {
        return $this->statut === self::STATUT_EN_TRAITEMENT;
    }

    public function peutEtreRejetee(): bool
    {
        return in_array($this->statut, [self::STATUT_EN_TRAITEMENT, self::STATUT_EN_ATTENTE]);
    }

    public function marquerEnTraitement(User $user): void
    {
        $this->update([
            'statut' => self::STATUT_EN_TRAITEMENT,
            'date_traitement' => now(),
            'user_treated_id' => $user->id
        ]);
    }

    public function valider(User $user, string $observations = null): void
    {
        $this->update([
            'statut' => self::STATUT_VALIDEE,
            'date_validation' => now(),
            'user_validated_id' => $user->id,
            'observations' => $observations
        ]);
    }

    public function rejeter(User $user, string $motif): void
    {
        $this->update([
            'statut' => self::STATUT_REJETEE,
            'motif_rejet' => $motif,
            'user_treated_id' => $user->id
        ]);
    }

    /**
     * Générer un numéro de déclaration unique
     */
    public static function generateNumeroDeclaration(): string
    {
        $annee = date('Y');
        $maxTentatives = 100; // Éviter les boucles infinies
        $tentative = 0;

        do {
            // Récupérer le dernier numéro pour cette année
            $dernierNumero = self::where('numero_declaration', 'like', "DECL{$annee}%")
                                ->orderBy('numero_declaration', 'desc')
                                ->first();

            if ($dernierNumero) {
                // Extraire le numéro séquentiel et l'incrémenter
                $numeroSequentiel = intval(substr($dernierNumero->numero_declaration, -3)) + 1;
            } else {
                // Premier numéro de l'année
                $numeroSequentiel = 1;
            }

            $numeroGenere = "DECL{$annee}" . str_pad($numeroSequentiel, 3, '0', STR_PAD_LEFT);

            // Vérifier si ce numéro existe déjà (double sécurité)
            $existe = self::where('numero_declaration', $numeroGenere)->exists();

            if (!$existe) {
                return $numeroGenere;
            }

            $tentative++;

            // Si le numéro existe, forcer une pause et réessayer
            usleep(10000); // 10ms de pause

        } while ($tentative < $maxTentatives);

        // En dernier recours, utiliser un timestamp pour garantir l'unicité
        return "DECL{$annee}" . str_pad(time() % 1000, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Formater la période
     */
    public static function formatPeriode(int $annee, int $mois): string
    {
        $moisNoms = [
            1 => 'Janvier', 2 => 'Février', 3 => 'Mars', 4 => 'Avril',
            5 => 'Mai', 6 => 'Juin', 7 => 'Juillet', 8 => 'Août',
            9 => 'Septembre', 10 => 'Octobre', 11 => 'Novembre', 12 => 'Décembre'
        ];

        return ($moisNoms[$mois] ?? 'Mois inconnu') . ' ' . $annee;
    }

    /**
     * Get route key name for Laravel
     */
    public function getRouteKeyName(): string
    {
        return 'id';
    }
}
