<?php

namespace Modules\Declarations\Repositories;

use Modules\Declarations\Models\Declaration;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class DeclarationRepository
{
    protected Declaration $model;

    public function __construct(Declaration $model)
    {
        $this->model = $model;
    }

    /**
     * Récupérer toutes les déclarations avec pagination et filtres
     */
    public function getAllWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery()
            ->with(['userCreated', 'userTreated', 'userValidated'])
            ->orderBy('date_reception', 'desc');

        // Filtre par employeur
        if (!empty($filters['employeur'])) {
            $query->parEmployeur($filters['employeur']);
        }

        // Filtre par statut
        if (!empty($filters['statut'])) {
            $query->parStatut($filters['statut']);
        }

        // Filtre par période
        if (!empty($filters['annee'])) {
            $query->parPeriode($filters['annee'], $filters['mois'] ?? null);
        }

        // Filtre par date de réception
        if (!empty($filters['date_debut'])) {
            $query->where('date_reception', '>=', $filters['date_debut']);
        }

        if (!empty($filters['date_fin'])) {
            $query->where('date_reception', '<=', $filters['date_fin']);
        }

        // Recherche textuelle
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('numero_declaration', 'like', "%{$search}%")
                  ->orWhere('employeur', 'like', "%{$search}%")
                  ->orWhere('siret_employeur', 'like', "%{$search}%");
            });
        }

        return $query->paginate($perPage);
    }

    /**
     * Trouver une déclaration par ID avec relations
     */
    public function findWithRelations(int $id): ?Declaration
    {
        return $this->model->with([
            'userCreated',
            'userTreated', 
            'userValidated',
            'declarationOriginale',
            'declarationsRectificatives'
        ])->find($id);
    }

    /**
     * Créer une nouvelle déclaration
     */
    public function create(array $data): Declaration
    {
        return $this->model->create($data);
    }

    /**
     * Mettre à jour une déclaration
     */
    public function update(Declaration $declaration, array $data): bool
    {
        return $declaration->update($data);
    }

    /**
     * Supprimer une déclaration (soft delete)
     */
    public function delete(Declaration $declaration): bool
    {
        return $declaration->delete();
    }

    /**
     * Statistiques des déclarations
     */
    public function getStatistics(): array
    {
        $stats = $this->model->selectRaw('
            COUNT(*) as total,
            COUNT(CASE WHEN statut = "en_attente" THEN 1 END) as en_attente,
            COUNT(CASE WHEN statut = "en_traitement" THEN 1 END) as en_traitement,
            COUNT(CASE WHEN statut = "validee" THEN 1 END) as validees,
            COUNT(CASE WHEN statut = "rejetee" THEN 1 END) as rejetees,
            SUM(montant_total) as montant_total,
            SUM(CASE WHEN MONTH(date_reception) = MONTH(NOW()) AND YEAR(date_reception) = YEAR(NOW()) THEN 1 ELSE 0 END) as recues_ce_mois
        ')->first();

        return [
            'total' => $stats->total ?? 0,
            'en_attente' => $stats->en_attente ?? 0,
            'en_traitement' => $stats->en_traitement ?? 0,
            'validees' => $stats->validees ?? 0,
            'rejetees' => $stats->rejetees ?? 0,
            'montant_total' => $stats->montant_total ?? 0,
            'recues_ce_mois' => $stats->recues_ce_mois ?? 0,
        ];
    }

    /**
     * Déclarations récentes
     */
    public function getRecentes(int $limit = 10): Collection
    {
        return $this->model->with(['userCreated'])
            ->recentes()
            ->orderBy('date_reception', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Top employeurs par montant
     */
    public function getTopEmployeurs(int $limit = 5): Collection
    {
        return $this->model->select('employeur')
            ->selectRaw('SUM(montant_total) as total_montant')
            ->selectRaw('COUNT(*) as nombre_declarations')
            ->selectRaw('SUM(nombre_employes) as total_employes')
            ->groupBy('employeur')
            ->orderBy('total_montant', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Évolution des déclarations par mois
     */
    public function getEvolutionParMois(int $annee = null): Collection
    {
        $annee = $annee ?? date('Y');
        
        return $this->model->selectRaw('
            mois,
            COUNT(*) as nombre,
            SUM(montant_total) as montant,
            AVG(nombre_employes) as moyenne_employes
        ')
        ->where('annee', $annee)
        ->groupBy('mois')
        ->orderBy('mois')
        ->get();
    }

    /**
     * Déclarations en retard
     */
    public function getEnRetard(): Collection
    {
        return $this->model->where('date_echeance', '<', now())
            ->whereIn('statut', [Declaration::STATUT_EN_ATTENTE, Declaration::STATUT_EN_TRAITEMENT])
            ->with(['userCreated'])
            ->orderBy('date_echeance')
            ->get();
    }

    /**
     * Recherche avancée
     */
    public function search(string $term): Collection
    {
        return $this->model->where(function ($query) use ($term) {
            $query->where('numero_declaration', 'like', "%{$term}%")
                  ->orWhere('employeur', 'like', "%{$term}%")
                  ->orWhere('siret_employeur', 'like', "%{$term}%")
                  ->orWhere('observations', 'like', "%{$term}%");
        })
        ->with(['userCreated'])
        ->orderBy('date_reception', 'desc')
        ->limit(20)
        ->get();
    }

    /**
     * Déclarations par statut
     */
    public function getParStatut(string $statut): Collection
    {
        return $this->model->parStatut($statut)
            ->with(['userCreated', 'userTreated'])
            ->orderBy('date_reception', 'desc')
            ->get();
    }

    /**
     * Vérifier si une déclaration existe pour un employeur et une période
     */
    public function existePourPeriode(string $employeur, int $annee, int $mois, int $excludeId = null): bool
    {
        $query = $this->model->where('employeur', $employeur)
            ->where('annee', $annee)
            ->where('mois', $mois);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Obtenir les années disponibles
     */
    public function getAnneesDisponibles(): array
    {
        return $this->model->distinct()
            ->orderBy('annee', 'desc')
            ->pluck('annee')
            ->toArray();
    }

    /**
     * Obtenir les employeurs uniques
     */
    public function getEmployeursUniques(): array
    {
        return $this->model->distinct()
            ->orderBy('employeur')
            ->pluck('employeur')
            ->toArray();
    }
}
