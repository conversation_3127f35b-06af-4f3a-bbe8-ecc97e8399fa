@extends('layouts.template')

@section('title', 'Nouvelle Déclaration - CRFM')

@section('page-header')
@section('page-title', 'Nouvelle Déclaration')
@section('page-description', 'Créer une nouvelle déclaration de cotisations')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Dossiers</a></li>
        <li class="breadcrumb-item"><a href="{{ route('declarations.index') }}">Déclarations</a></li>
        <li class="breadcrumb-item"><a href="#!">Nouvelle</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-plus text-c-green me-2"></i>Nouvelle Déclaration</h5>
                <div class="card-header-right">
                    <a href="{{ route('declarations.index') }}" class="btn btn-secondary btn-sm">
                        <i class="feather icon-arrow-left me-1"></i>Retour à la liste
                    </a>
                </div>
            </div>
            <div class="card-block">
                <form action="{{ route('declarations.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    
                    <!-- Informations de l'employeur -->
                    <div class="row">
                        <div class="col-md-12">
                            <h6 class="text-muted mb-3"><i class="feather icon-building me-2"></i>Informations de l'employeur</h6>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Employeur <span class="text-danger">*</span></label>
                                <input type="text" name="employeur" class="form-control {{ $errors->has('employeur') ? 'is-invalid' : '' }}" 
                                       value="{{ old('employeur') }}" placeholder="Nom de l'employeur" required>
                                @if($errors->has('employeur'))
                                    <div class="invalid-feedback">{{ $errors->first('employeur') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">SIRET</label>
                                <input type="text" name="siret_employeur" class="form-control {{ $errors->has('siret_employeur') ? 'is-invalid' : '' }}" 
                                       value="{{ old('siret_employeur') }}" placeholder="14 chiffres" maxlength="14">
                                @if($errors->has('siret_employeur'))
                                    <div class="invalid-feedback">{{ $errors->first('siret_employeur') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="form-label">Adresse de l'employeur</label>
                                <textarea name="adresse_employeur" class="form-control {{ $errors->has('adresse_employeur') ? 'is-invalid' : '' }}" 
                                          rows="2" placeholder="Adresse complète">{{ old('adresse_employeur') }}</textarea>
                                @if($errors->has('adresse_employeur'))
                                    <div class="invalid-feedback">{{ $errors->first('adresse_employeur') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Période de déclaration -->
                    <div class="row">
                        <div class="col-md-12">
                            <h6 class="text-muted mb-3"><i class="feather icon-calendar me-2"></i>Période de déclaration</h6>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Année <span class="text-danger">*</span></label>
                                <select name="annee" class="form-control {{ $errors->has('annee') ? 'is-invalid' : '' }}" required>
                                    <option value="">Sélectionner une année</option>
                                    @for($year = date('Y'); $year >= 2020; $year--)
                                        <option value="{{ $year }}" {{ old('annee') == $year ? 'selected' : '' }}>
                                            {{ $year }}
                                        </option>
                                    @endfor
                                </select>
                                @if($errors->has('annee'))
                                    <div class="invalid-feedback">{{ $errors->first('annee') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Mois <span class="text-danger">*</span></label>
                                <select name="mois" class="form-control {{ $errors->has('mois') ? 'is-invalid' : '' }}" required>
                                    <option value="">Sélectionner un mois</option>
                                    @php
                                        $mois = [
                                            1 => 'Janvier', 2 => 'Février', 3 => 'Mars', 4 => 'Avril',
                                            5 => 'Mai', 6 => 'Juin', 7 => 'Juillet', 8 => 'Août',
                                            9 => 'Septembre', 10 => 'Octobre', 11 => 'Novembre', 12 => 'Décembre'
                                        ];
                                    @endphp
                                    @foreach($mois as $num => $nom)
                                        <option value="{{ $num }}" {{ old('mois') == $num ? 'selected' : '' }}>
                                            {{ $nom }}
                                        </option>
                                    @endforeach
                                </select>
                                @if($errors->has('mois'))
                                    <div class="invalid-feedback">{{ $errors->first('mois') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Données de la déclaration -->
                    <div class="row">
                        <div class="col-md-12">
                            <h6 class="text-muted mb-3"><i class="feather icon-file-text me-2"></i>Données de la déclaration</h6>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Nombre d'employés <span class="text-danger">*</span></label>
                                <input type="number" name="nombre_employes" class="form-control {{ $errors->has('nombre_employes') ? 'is-invalid' : '' }}" 
                                       value="{{ old('nombre_employes') }}" min="0" placeholder="0" required>
                                @if($errors->has('nombre_employes'))
                                    <div class="invalid-feedback">{{ $errors->first('nombre_employes') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Montant total (€) <span class="text-danger">*</span></label>
                                <input type="number" name="montant_total" class="form-control {{ $errors->has('montant_total') ? 'is-invalid' : '' }}" 
                                       value="{{ old('montant_total') }}" min="0" step="0.01" placeholder="0.00" required>
                                @if($errors->has('montant_total'))
                                    <div class="invalid-feedback">{{ $errors->first('montant_total') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Date de réception</label>
                                <input type="date" name="date_reception" class="form-control {{ $errors->has('date_reception') ? 'is-invalid' : '' }}" 
                                       value="{{ old('date_reception', date('Y-m-d')) }}">
                                @if($errors->has('date_reception'))
                                    <div class="invalid-feedback">{{ $errors->first('date_reception') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Cotisations salariales (€)</label>
                                <input type="number" name="montant_cotisations_salariales" class="form-control {{ $errors->has('montant_cotisations_salariales') ? 'is-invalid' : '' }}" 
                                       value="{{ old('montant_cotisations_salariales') }}" min="0" step="0.01" placeholder="0.00">
                                @if($errors->has('montant_cotisations_salariales'))
                                    <div class="invalid-feedback">{{ $errors->first('montant_cotisations_salariales') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Cotisations patronales (€)</label>
                                <input type="number" name="montant_cotisations_patronales" class="form-control {{ $errors->has('montant_cotisations_patronales') ? 'is-invalid' : '' }}" 
                                       value="{{ old('montant_cotisations_patronales') }}" min="0" step="0.01" placeholder="0.00">
                                @if($errors->has('montant_cotisations_patronales'))
                                    <div class="invalid-feedback">{{ $errors->first('montant_cotisations_patronales') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="form-label">Date d'échéance</label>
                                <input type="date" name="date_echeance" class="form-control {{ $errors->has('date_echeance') ? 'is-invalid' : '' }}" 
                                       value="{{ old('date_echeance') }}">
                                @if($errors->has('date_echeance'))
                                    <div class="invalid-feedback">{{ $errors->first('date_echeance') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Fichiers -->
                    <div class="row">
                        <div class="col-md-12">
                            <h6 class="text-muted mb-3"><i class="feather icon-upload me-2"></i>Documents</h6>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Fichier de déclaration</label>
                                <input type="file" name="fichier_declaration" class="form-control {{ $errors->has('fichier_declaration') ? 'is-invalid' : '' }}" 
                                       accept=".pdf,.doc,.docx,.xls,.xlsx">
                                <small class="form-text text-muted">Formats acceptés: PDF, DOC, DOCX, XLS, XLSX (max 10MB)</small>
                                @if($errors->has('fichier_declaration'))
                                    <div class="invalid-feedback">{{ $errors->first('fichier_declaration') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Fichier de justificatifs</label>
                                <input type="file" name="fichier_justificatifs" class="form-control {{ $errors->has('fichier_justificatifs') ? 'is-invalid' : '' }}" 
                                       accept=".pdf,.doc,.docx,.xls,.xlsx,.zip">
                                <small class="form-text text-muted">Formats acceptés: PDF, DOC, DOCX, XLS, XLSX, ZIP (max 20MB)</small>
                                @if($errors->has('fichier_justificatifs'))
                                    <div class="invalid-feedback">{{ $errors->first('fichier_justificatifs') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Observations -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="form-label">Observations</label>
                                <textarea name="observations" class="form-control {{ $errors->has('observations') ? 'is-invalid' : '' }}" 
                                          rows="3" placeholder="Commentaires ou observations...">{{ old('observations') }}</textarea>
                                @if($errors->has('observations'))
                                    <div class="invalid-feedback">{{ $errors->first('observations') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Boutons d'action -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group text-end">
                                <a href="{{ route('declarations.index') }}" class="btn btn-secondary me-2">
                                    <i class="feather icon-x me-1"></i>Annuler
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="feather icon-save me-1"></i>Créer la déclaration
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Validation côté client
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const montantTotal = document.querySelector('input[name="montant_total"]');
    const cotisationsSalariales = document.querySelector('input[name="montant_cotisations_salariales"]');
    const cotisationsPatronales = document.querySelector('input[name="montant_cotisations_patronales"]');
    
    function validateMontants() {
        const total = parseFloat(montantTotal.value) || 0;
        const salariales = parseFloat(cotisationsSalariales.value) || 0;
        const patronales = parseFloat(cotisationsPatronales.value) || 0;
        
        if (salariales + patronales > total && total > 0) {
            montantTotal.setCustomValidity('Le montant total ne peut pas être inférieur à la somme des cotisations.');
        } else {
            montantTotal.setCustomValidity('');
        }
    }
    
    [montantTotal, cotisationsSalariales, cotisationsPatronales].forEach(input => {
        input.addEventListener('input', validateMontants);
    });
    
    // Format SIRET
    const siretInput = document.querySelector('input[name="siret_employeur"]');
    siretInput.addEventListener('input', function() {
        this.value = this.value.replace(/\D/g, '').substring(0, 14);
    });
});
</script>
@endpush
