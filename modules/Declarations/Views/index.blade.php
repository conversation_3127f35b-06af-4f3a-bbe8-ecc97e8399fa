@extends('layouts.template')

@section('title', 'Déclarations - CRFM')

@section('page-header')
@section('page-title', 'Gestion des Déclarations')
@section('page-description', 'Traitement des déclarations de cotisations des employeurs')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Dossiers</a></li>
        <li class="breadcrumb-item"><a href="#!">Déclarations</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <!-- Statistiques rapides -->
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-green text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($statistics['recues_ce_mois'] ?? 0) }}</h4>
                        <h6 class="text-white m-b-0">Reçues ce mois</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-file-text f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-blue text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($statistics['en_traitement'] ?? 0) }}</h4>
                        <h6 class="text-white m-b-0">En traitement</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-clock f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-yellow text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($statistics['validees'] ?? 0) }}</h4>
                        <h6 class="text-white m-b-0">Validées</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-check-circle f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card text-white" style="background: linear-gradient(135deg, #4a148c 0%, #6a1b9a 100%) !important;">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="f-w-600" style="color: #ffffff !important; font-weight: 600; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">{{ number_format(($statistics['montant_total'] ?? 0) / 1000000, 1) }}M</h4>
                        <h6 class="m-b-0" style="color: #ffffff !important; text-shadow: 1px 1px 3px rgba(0,0,0,0.5); font-weight: 500;">Montant €</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-dollar-sign f-28" style="color: #ffffff !important; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Filtres et recherche -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-filter text-c-blue me-2"></i>Filtres et recherche</h5>
                <div class="card-header-right">
                    <a href="{{ route('declarations.create') }}" class="btn btn-success btn-sm">
                        <i class="feather icon-plus me-1"></i>Nouvelle déclaration
                    </a>
                </div>
            </div>
            <div class="card-block">
                <form method="GET" action="{{ route('declarations.index') }}">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Employeur</label>
                                <select name="employeur" class="form-control">
                                    <option value="">Tous les employeurs</option>
                                    @foreach($employeursUniques as $employeur)
                                        <option value="{{ $employeur }}" {{ ($filters['employeur'] ?? '') === $employeur ? 'selected' : '' }}>
                                            {{ $employeur }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Année</label>
                                <select name="annee" class="form-control">
                                    <option value="">Toutes</option>
                                    @foreach($anneesDisponibles as $annee)
                                        <option value="{{ $annee }}" {{ ($filters['annee'] ?? '') == $annee ? 'selected' : '' }}>
                                            {{ $annee }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Mois</label>
                                <select name="mois" class="form-control">
                                    <option value="">Tous</option>
                                    @for($i = 1; $i <= 12; $i++)
                                        <option value="{{ $i }}" {{ ($filters['mois'] ?? '') == $i ? 'selected' : '' }}>
                                            {{ DateTime::createFromFormat('!m', $i)->format('F') }}
                                        </option>
                                    @endfor
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Statut</label>
                                <select name="statut" class="form-control">
                                    <option value="">Tous les statuts</option>
                                    <option value="en_attente" {{ ($filters['statut'] ?? '') === 'en_attente' ? 'selected' : '' }}>En attente</option>
                                    <option value="en_traitement" {{ ($filters['statut'] ?? '') === 'en_traitement' ? 'selected' : '' }}>En traitement</option>
                                    <option value="validee" {{ ($filters['statut'] ?? '') === 'validee' ? 'selected' : '' }}>Validée</option>
                                    <option value="rejetee" {{ ($filters['statut'] ?? '') === 'rejetee' ? 'selected' : '' }}>Rejetée</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Recherche</label>
                                <div class="input-group">
                                    <input type="text" name="search" class="form-control" placeholder="N° déclaration, employeur..." value="{{ $filters['search'] ?? '' }}">
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="feather icon-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <a href="{{ route('declarations.index') }}" class="btn btn-secondary btn-sm">
                                <i class="feather icon-refresh-cw me-1"></i>Réinitialiser
                            </a>
                            <button type="button" class="btn btn-info btn-sm ms-2" onclick="exportDeclarations()">
                                <i class="feather icon-download me-1"></i>Exporter
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Liste des déclarations -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-list text-c-blue me-2"></i>Déclarations ({{ $declarations->total() }})</h5>
            </div>
            <div class="card-block">
                @if($declarations->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>N° Déclaration</th>
                                    <th>Employeur</th>
                                    <th>Période</th>
                                    <th>Nb Employés</th>
                                    <th>Montant Total</th>
                                    <th>Date Réception</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($declarations as $declaration)
                                    <tr>
                                        <td>
                                            <strong>{{ $declaration->numero_declaration }}</strong>
                                        </td>
                                        <td>
                                            <div class="d-inline-block align-middle">
                                                <h6 class="m-b-0">{{ Str::limit($declaration->employeur, 30) }}</h6>
                                                @if($declaration->siret_employeur)
                                                    <p class="m-b-0 text-muted">{{ $declaration->siret_employeur }}</p>
                                                @endif
                                            </div>
                                        </td>
                                        <td>{{ $declaration->periode_formatted }}</td>
                                        <td>{{ number_format($declaration->nombre_employes) }}</td>
                                        <td>{{ $declaration->montant_total_formate }}</td>
                                        <td>{{ $declaration->date_reception->format('d/m/Y') }}</td>
                                        <td>
                                            <span class="badge bg-{{ $declaration->statut_color }}">
                                                {{ $declaration->statut_label }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('declarations.show', $declaration) }}" class="btn btn-sm btn-primary" title="Voir">
                                                    <i class="feather icon-eye"></i>
                                                </a>
                                                @if($declaration->peutEtreModifiee())
                                                    <a href="{{ route('declarations.edit', $declaration) }}" class="btn btn-sm btn-warning" title="Modifier">
                                                        <i class="feather icon-edit"></i>
                                                    </a>
                                                @endif
                                                @if($declaration->statut === 'en_attente')
                                                    <button type="button" class="btn btn-sm btn-info" onclick="marquerEnTraitement({{ $declaration->id }})" title="Mettre en traitement">
                                                        <i class="feather icon-play"></i>
                                                    </button>
                                                @endif
                                                @if($declaration->peutEtreValidee())
                                                    <button type="button" class="btn btn-sm btn-success" onclick="validerDeclaration({{ $declaration->id }})" title="Valider">
                                                        <i class="feather icon-check"></i>
                                                    </button>
                                                @endif
                                                @if($declaration->peutEtreRejetee())
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="rejeterDeclaration({{ $declaration->id }})" title="Rejeter">
                                                        <i class="feather icon-x"></i>
                                                    </button>
                                                @endif
                                                <button type="button" class="btn btn-sm btn-secondary" onclick="supprimerDeclaration({{ $declaration->id }})" title="Supprimer">
                                                    <i class="feather icon-trash-2"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            Affichage de {{ $declarations->firstItem() }} à {{ $declarations->lastItem() }} 
                            sur {{ $declarations->total() }} résultats
                        </div>
                        <div>
                            {{ $declarations->appends(request()->query())->links() }}
                        </div>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="feather icon-file-text f-40 text-muted"></i>
                        <h5 class="mt-3">Aucune déclaration trouvée</h5>
                        <p class="text-muted">Aucune déclaration ne correspond aux critères de recherche.</p>
                        <a href="{{ route('declarations.create') }}" class="btn btn-primary">
                            <i class="feather icon-plus me-2"></i>Créer une déclaration
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@if($topEmployeurs->count() > 0)
<div class="row">
    <!-- Top Employeurs -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-trending-up text-c-green me-2"></i>Top Employeurs</h5>
            </div>
            <div class="card-block">
                @foreach($topEmployeurs as $employeur)
                    <div class="row align-items-center m-b-25">
                        <div class="col-8">
                            <h6 class="m-b-5">{{ Str::limit($employeur->employeur, 40) }}</h6>
                            <p class="text-muted m-b-0">{{ number_format($employeur->total_employes) }} employés • {{ $employeur->nombre_declarations }} déclarations</p>
                        </div>
                        <div class="col-4 text-end">
                            <h6 class="m-b-0">{{ number_format($employeur->total_montant / 1000000, 1) }}M €</h6>
                        </div>
                    </div>
                    @if(!$loop->last)
                        <div class="progress m-b-25" style="height: 4px;">
                            <div class="progress-bar bg-c-blue" style="width: {{ ($employeur->total_montant / $topEmployeurs->first()->total_montant) * 100 }}%"></div>
                        </div>
                    @endif
                @endforeach
            </div>
        </div>
    </div>
</div>
@endif

<!-- Modals -->
<!-- Modal de validation -->
<div class="modal fade" id="validerModal" tabindex="-1" aria-labelledby="validerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="validerModalLabel">Valider la déclaration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="validerForm" method="POST">
                @csrf
                @method('PATCH')
                <div class="modal-body">
                    <div class="form-group mb-3">
                        <label class="form-label">Observations (optionnel)</label>
                        <textarea name="observations" class="form-control" rows="3"
                                  placeholder="Commentaires sur la validation..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-success">Valider</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal de rejet -->
<div class="modal fade" id="rejeterModal" tabindex="-1" aria-labelledby="rejeterModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejeterModalLabel">Rejeter la déclaration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="rejeterForm" method="POST">
                @csrf
                @method('PATCH')
                <div class="modal-body">
                    <div class="form-group mb-3">
                        <label class="form-label">Motif de rejet (obligatoire)</label>
                        <textarea name="motif_rejet" class="form-control" rows="3"
                                  placeholder="Précisez le motif du rejet..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-danger">Rejeter</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function marquerEnTraitement(declarationId) {
    if (confirm('Êtes-vous sûr de vouloir mettre cette déclaration en traitement ?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/declarations/${declarationId}/traitement`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';

        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'PATCH';

        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
}

function validerDeclaration(declarationId) {
    const form = document.getElementById('validerForm');
    form.action = `/declarations/${declarationId}/valider`;

    const modal = new bootstrap.Modal(document.getElementById('validerModal'));
    modal.show();
}

function rejeterDeclaration(declarationId) {
    const form = document.getElementById('rejeterForm');
    form.action = `/declarations/${declarationId}/rejeter`;

    const modal = new bootstrap.Modal(document.getElementById('rejeterModal'));
    modal.show();
}

function supprimerDeclaration(declarationId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette déclaration ? Cette action est irréversible.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/declarations/${declarationId}`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';

        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';

        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
}

function exportDeclarations() {
    // Récupérer les filtres actuels
    const params = new URLSearchParams(window.location.search);
    const exportUrl = '{{ route("declarations.export") }}?' + params.toString();

    // Ouvrir dans un nouvel onglet
    window.open(exportUrl, '_blank');
}
</script>
@endpush
