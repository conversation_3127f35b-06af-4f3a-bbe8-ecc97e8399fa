@extends('layouts.template')

@section('title', 'Déclaration ' . $declaration->numero_declaration . ' - CRFM')

@section('page-header')
@section('page-title', 'Déclaration ' . $declaration->numero_declaration)
@section('page-description', 'Détails de la déclaration de ' . $declaration->employeur)
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Dossiers</a></li>
        <li class="breadcrumb-item"><a href="{{ route('declarations.index') }}">Déclarations</a></li>
        <li class="breadcrumb-item"><a href="#!">{{ $declaration->numero_declaration }}</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <!-- Informations principales -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-file-text text-c-blue me-2"></i>Informations de la déclaration</h5>
                <div class="card-header-right">
                    <span class="badge bg-{{ $declaration->statut_color }} badge-lg">
                        {{ $declaration->statut_label }}
                    </span>
                </div>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="text-muted"><strong>N° Déclaration:</strong></td>
                                <td>{{ $declaration->numero_declaration }}</td>
                            </tr>
                            <tr>
                                <td class="text-muted"><strong>Employeur:</strong></td>
                                <td>{{ $declaration->employeur }}</td>
                            </tr>
                            @if($declaration->siret_employeur)
                            <tr>
                                <td class="text-muted"><strong>SIRET:</strong></td>
                                <td>{{ $declaration->siret_employeur }}</td>
                            </tr>
                            @endif
                            <tr>
                                <td class="text-muted"><strong>Période:</strong></td>
                                <td>{{ $declaration->periode_formatted }}</td>
                            </tr>
                            <tr>
                                <td class="text-muted"><strong>Nombre d'employés:</strong></td>
                                <td>{{ number_format($declaration->nombre_employes) }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="text-muted"><strong>Montant total:</strong></td>
                                <td><strong class="text-success">{{ $declaration->montant_total_formate }}</strong></td>
                            </tr>
                            @if($declaration->montant_cotisations_salariales > 0)
                            <tr>
                                <td class="text-muted"><strong>Cotisations salariales:</strong></td>
                                <td>{{ number_format($declaration->montant_cotisations_salariales, 0, ',', ' ') }} €</td>
                            </tr>
                            @endif
                            @if($declaration->montant_cotisations_patronales > 0)
                            <tr>
                                <td class="text-muted"><strong>Cotisations patronales:</strong></td>
                                <td>{{ number_format($declaration->montant_cotisations_patronales, 0, ',', ' ') }} €</td>
                            </tr>
                            @endif
                            <tr>
                                <td class="text-muted"><strong>Date de réception:</strong></td>
                                <td>{{ $declaration->date_reception->format('d/m/Y') }}</td>
                            </tr>
                            @if($declaration->date_echeance)
                            <tr>
                                <td class="text-muted"><strong>Date d'échéance:</strong></td>
                                <td>{{ $declaration->date_echeance->format('d/m/Y') }}</td>
                            </tr>
                            @endif
                        </table>
                    </div>
                </div>

                @if($declaration->adresse_employeur)
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h6 class="text-muted">Adresse de l'employeur</h6>
                        <p>{{ $declaration->adresse_employeur }}</p>
                    </div>
                </div>
                @endif

                @if($declaration->observations)
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h6 class="text-muted">Observations</h6>
                        <p>{{ $declaration->observations }}</p>
                    </div>
                </div>
                @endif

                @if($declaration->motif_rejet)
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="alert alert-danger">
                            <h6 class="alert-heading">Motif de rejet</h6>
                            <p class="mb-0">{{ $declaration->motif_rejet }}</p>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Documents -->
        @if($declaration->fichier_declaration || $declaration->fichier_justificatifs)
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-paperclip text-c-green me-2"></i>Documents attachés</h5>
            </div>
            <div class="card-block">
                <div class="row">
                    @if($declaration->fichier_declaration)
                    <div class="col-md-6">
                        <div class="d-flex align-items-center p-3 border rounded">
                            <i class="feather icon-file-text f-24 text-c-blue me-3"></i>
                            <div>
                                <h6 class="mb-1">Fichier de déclaration</h6>
                                <p class="text-muted mb-0">{{ basename($declaration->fichier_declaration) }}</p>
                            </div>
                            <div class="ms-auto">
                                <a href="{{ asset('storage/' . $declaration->fichier_declaration) }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="feather icon-download"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    @endif
                    @if($declaration->fichier_justificatifs)
                    <div class="col-md-6">
                        <div class="d-flex align-items-center p-3 border rounded">
                            <i class="feather icon-file-text f-24 text-c-green me-3"></i>
                            <div>
                                <h6 class="mb-1">Fichier de justificatifs</h6>
                                <p class="text-muted mb-0">{{ basename($declaration->fichier_justificatifs) }}</p>
                            </div>
                            <div class="ms-auto">
                                <a href="{{ asset('storage/' . $declaration->fichier_justificatifs) }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="feather icon-download"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- Actions et historique -->
    <div class="col-md-4">
        <!-- Actions -->
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-settings text-c-purple me-2"></i>Actions</h5>
            </div>
            <div class="card-block">
                <div class="d-grid gap-2">
                    @if($declaration->peutEtreModifiee())
                        <a href="{{ route('declarations.edit', $declaration) }}" class="btn btn-warning">
                            <i class="feather icon-edit me-2"></i>Modifier
                        </a>
                    @endif

                    @if($declaration->statut === 'en_attente')
                        <button type="button" class="btn btn-info" onclick="marquerEnTraitement({{ $declaration->id }})">
                            <i class="feather icon-play me-2"></i>Mettre en traitement
                        </button>
                    @endif

                    @if($declaration->peutEtreValidee())
                        <button type="button" class="btn btn-success" onclick="validerDeclaration({{ $declaration->id }})">
                            <i class="feather icon-check me-2"></i>Valider
                        </button>
                    @endif

                    @if($declaration->peutEtreRejetee())
                        <button type="button" class="btn btn-danger" onclick="rejeterDeclaration({{ $declaration->id }})">
                            <i class="feather icon-x me-2"></i>Rejeter
                        </button>
                    @endif

                    <button type="button" class="btn btn-secondary" onclick="supprimerDeclaration({{ $declaration->id }})">
                        <i class="feather icon-trash-2 me-2"></i>Supprimer
                    </button>

                    <a href="{{ route('declarations.index') }}" class="btn btn-outline-secondary">
                        <i class="feather icon-arrow-left me-2"></i>Retour à la liste
                    </a>
                </div>
            </div>
        </div>

        <!-- Historique -->
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-clock text-c-orange me-2"></i>Historique</h5>
            </div>
            <div class="card-block">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Déclaration créée</h6>
                            <p class="timeline-text">{{ $declaration->created_at->format('d/m/Y à H:i') }}</p>
                            @if($declaration->userCreated)
                                <small class="text-muted">Par {{ $declaration->userCreated->name }}</small>
                            @endif
                        </div>
                    </div>

                    @if($declaration->date_traitement)
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Mise en traitement</h6>
                            <p class="timeline-text">{{ $declaration->date_traitement->format('d/m/Y à H:i') }}</p>
                            @if($declaration->userTreated)
                                <small class="text-muted">Par {{ $declaration->userTreated->name }}</small>
                            @endif
                        </div>
                    </div>
                    @endif

                    @if($declaration->date_validation)
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Déclaration validée</h6>
                            <p class="timeline-text">{{ $declaration->date_validation->format('d/m/Y à H:i') }}</p>
                            @if($declaration->userValidated)
                                <small class="text-muted">Par {{ $declaration->userValidated->name }}</small>
                            @endif
                        </div>
                    </div>
                    @endif

                    @if($declaration->statut === 'rejetee')
                    <div class="timeline-item">
                        <div class="timeline-marker bg-danger"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Déclaration rejetée</h6>
                            <p class="timeline-text">{{ $declaration->updated_at->format('d/m/Y à H:i') }}</p>
                            @if($declaration->userTreated)
                                <small class="text-muted">Par {{ $declaration->userTreated->name }}</small>
                            @endif
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals (réutilisation des modals de l'index) -->
@include('declarations::partials.modals')
@endsection

@push('scripts')
<script>
function marquerEnTraitement(declarationId) {
    if (confirm('Êtes-vous sûr de vouloir mettre cette déclaration en traitement ?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/declarations/${declarationId}/traitement`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'PATCH';
        
        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
}

function validerDeclaration(declarationId) {
    const form = document.getElementById('validerForm');
    form.action = `/declarations/${declarationId}/valider`;
    
    const modal = new bootstrap.Modal(document.getElementById('validerModal'));
    modal.show();
}

function rejeterDeclaration(declarationId) {
    const form = document.getElementById('rejeterForm');
    form.action = `/declarations/${declarationId}/rejeter`;
    
    const modal = new bootstrap.Modal(document.getElementById('rejeterModal'));
    modal.show();
}

function supprimerDeclaration(declarationId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette déclaration ? Cette action est irréversible.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/declarations/${declarationId}`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        
        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e9ecef;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

.timeline-title {
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: 600;
}

.timeline-text {
    margin-bottom: 5px;
    font-size: 13px;
    color: #6c757d;
}
</style>
@endpush
