<?php

use Illuminate\Support\Facades\Route;
use Modules\Declarations\Controllers\DeclarationController;

/*
|--------------------------------------------------------------------------
| Declarations Module Routes
|--------------------------------------------------------------------------
*/

Route::middleware(['web', 'auth'])->group(function () {

    // Routes spécifiques (DOIVENT être avant les routes avec paramètres)
    Route::get('/declarations/export', [DeclarationController::class, 'export'])->name('declarations.export');
    Route::get('/declarations/create', [DeclarationController::class, 'create'])->name('declarations.create');

    // Routes de base CRUD
    Route::get('/declarations', [DeclarationController::class, 'index'])->name('declarations.index');
    Route::post('/declarations', [DeclarationController::class, 'store'])->name('declarations.store');
    Route::get('/declarations/{declaration}', [DeclarationController::class, 'show'])->name('declarations.show');
    Route::get('/declarations/{declaration}/edit', [DeclarationController::class, 'edit'])->name('declarations.edit');
    Route::put('/declarations/{declaration}', [DeclarationController::class, 'update'])->name('declarations.update');
    Route::delete('/declarations/{declaration}', [DeclarationController::class, 'destroy'])->name('declarations.destroy');

    // Actions spécifiques sur les déclarations
    Route::patch('/declarations/{declaration}/traitement', [DeclarationController::class, 'marquerEnTraitement'])->name('declarations.traitement');
    Route::patch('/declarations/{declaration}/valider', [DeclarationController::class, 'valider'])->name('declarations.valider');
    Route::patch('/declarations/{declaration}/rejeter', [DeclarationController::class, 'rejeter'])->name('declarations.rejeter');

    // API Routes pour AJAX
    Route::prefix('api/declarations')->name('api.declarations.')->group(function () {
        Route::get('/statistics', [DeclarationController::class, 'apiStatistics'])->name('statistics');
        Route::get('/search', [DeclarationController::class, 'apiSearch'])->name('search');
        Route::get('/evolution', [DeclarationController::class, 'apiEvolution'])->name('evolution');
    });

});
