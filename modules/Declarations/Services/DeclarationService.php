<?php

namespace Modules\Declarations\Services;

use Modules\Declarations\Models\Declaration;
use Modules\Declarations\Repositories\DeclarationRepository;
use App\Models\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Exception;

class DeclarationService
{
    protected DeclarationRepository $repository;

    public function __construct(DeclarationRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Créer une nouvelle déclaration
     */
    public function createDeclaration(array $data, User $user): Declaration
    {
        try {
            DB::beginTransaction();

            // Vérifier si une déclaration existe déjà pour cette période
            if ($this->repository->existePourPeriode($data['employeur'], $data['annee'], $data['mois'])) {
                throw new Exception("Une déclaration existe déjà pour cet employeur et cette période.");
            }

            // Préparer les données
            $declarationData = $this->prepareDeclarationData($data, $user);

            // Générer un numéro unique avec retry en cas de conflit
            $declarationData['numero_declaration'] = $this->generateUniqueNumero();

            // Gérer les fichiers uploadés
            if (isset($data['fichier_declaration'])) {
                $declarationData['fichier_declaration'] = $this->handleFileUpload(
                    $data['fichier_declaration'], 
                    'declarations'
                );
            }

            if (isset($data['fichier_justificatifs'])) {
                $declarationData['fichier_justificatifs'] = $this->handleFileUpload(
                    $data['fichier_justificatifs'], 
                    'justificatifs'
                );
            }

            // Créer la déclaration
            $declaration = $this->repository->create($declarationData);

            // Log de l'action
            Log::info('Déclaration créée', [
                'declaration_id' => $declaration->id,
                'numero' => $declaration->numero_declaration,
                'employeur' => $declaration->employeur,
                'user_id' => $user->id
            ]);

            DB::commit();
            return $declaration;

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de la création de la déclaration', [
                'error' => $e->getMessage(),
                'data' => $data,
                'user_id' => $user->id
            ]);
            throw $e;
        }
    }

    /**
     * Mettre à jour une déclaration
     */
    public function updateDeclaration(Declaration $declaration, array $data, User $user): Declaration
    {
        try {
            DB::beginTransaction();

            // Vérifier si la déclaration peut être modifiée
            if (!$declaration->peutEtreModifiee()) {
                throw new Exception("Cette déclaration ne peut plus être modifiée (statut: {$declaration->statut_label}).");
            }

            // Vérifier l'unicité pour la période (exclure la déclaration actuelle)
            if ($this->repository->existePourPeriode(
                $data['employeur'], 
                $data['annee'], 
                $data['mois'], 
                $declaration->id
            )) {
                throw new Exception("Une autre déclaration existe déjà pour cet employeur et cette période.");
            }

            // Préparer les données
            $declarationData = $this->prepareDeclarationData($data, $user, false);

            // Gérer les nouveaux fichiers
            if (isset($data['fichier_declaration']) && $data['fichier_declaration'] instanceof UploadedFile) {
                // Supprimer l'ancien fichier
                if ($declaration->fichier_declaration) {
                    Storage::delete($declaration->fichier_declaration);
                }
                $declarationData['fichier_declaration'] = $this->handleFileUpload(
                    $data['fichier_declaration'], 
                    'declarations'
                );
            }

            if (isset($data['fichier_justificatifs']) && $data['fichier_justificatifs'] instanceof UploadedFile) {
                // Supprimer l'ancien fichier
                if ($declaration->fichier_justificatifs) {
                    Storage::delete($declaration->fichier_justificatifs);
                }
                $declarationData['fichier_justificatifs'] = $this->handleFileUpload(
                    $data['fichier_justificatifs'], 
                    'justificatifs'
                );
            }

            // Mettre à jour
            $this->repository->update($declaration, $declarationData);
            $declaration->refresh();

            // Log de l'action
            Log::info('Déclaration mise à jour', [
                'declaration_id' => $declaration->id,
                'numero' => $declaration->numero_declaration,
                'user_id' => $user->id
            ]);

            DB::commit();
            return $declaration;

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de la mise à jour de la déclaration', [
                'error' => $e->getMessage(),
                'declaration_id' => $declaration->id,
                'user_id' => $user->id
            ]);
            throw $e;
        }
    }

    /**
     * Marquer une déclaration en traitement
     */
    public function marquerEnTraitement(Declaration $declaration, User $user): void
    {
        if ($declaration->statut !== Declaration::STATUT_EN_ATTENTE) {
            throw new Exception("Cette déclaration ne peut pas être mise en traitement (statut actuel: {$declaration->statut_label}).");
        }

        $declaration->marquerEnTraitement($user);

        Log::info('Déclaration mise en traitement', [
            'declaration_id' => $declaration->id,
            'numero' => $declaration->numero_declaration,
            'user_id' => $user->id
        ]);
    }

    /**
     * Valider une déclaration
     */
    public function validerDeclaration(Declaration $declaration, User $user, string $observations = null): void
    {
        if (!$declaration->peutEtreValidee()) {
            throw new Exception("Cette déclaration ne peut pas être validée (statut actuel: {$declaration->statut_label}).");
        }

        $declaration->valider($user, $observations);

        Log::info('Déclaration validée', [
            'declaration_id' => $declaration->id,
            'numero' => $declaration->numero_declaration,
            'user_id' => $user->id
        ]);
    }

    /**
     * Rejeter une déclaration
     */
    public function rejeterDeclaration(Declaration $declaration, User $user, string $motif): void
    {
        if (!$declaration->peutEtreRejetee()) {
            throw new Exception("Cette déclaration ne peut pas être rejetée (statut actuel: {$declaration->statut_label}).");
        }

        $declaration->rejeter($user, $motif);

        Log::info('Déclaration rejetée', [
            'declaration_id' => $declaration->id,
            'numero' => $declaration->numero_declaration,
            'motif' => $motif,
            'user_id' => $user->id
        ]);
    }

    /**
     * Supprimer une déclaration
     */
    public function deleteDeclaration(Declaration $declaration, User $user): void
    {
        try {
            DB::beginTransaction();

            // Supprimer les fichiers associés
            if ($declaration->fichier_declaration) {
                Storage::delete($declaration->fichier_declaration);
            }
            if ($declaration->fichier_justificatifs) {
                Storage::delete($declaration->fichier_justificatifs);
            }

            // Supprimer la déclaration (soft delete)
            $this->repository->delete($declaration);

            Log::info('Déclaration supprimée', [
                'declaration_id' => $declaration->id,
                'numero' => $declaration->numero_declaration,
                'user_id' => $user->id
            ]);

            DB::commit();

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de la suppression de la déclaration', [
                'error' => $e->getMessage(),
                'declaration_id' => $declaration->id,
                'user_id' => $user->id
            ]);
            throw $e;
        }
    }

    /**
     * Obtenir les statistiques
     */
    public function getStatistics(): array
    {
        return $this->repository->getStatistics();
    }

    /**
     * Préparer les données de déclaration
     */
    private function prepareDeclarationData(array $data, User $user, bool $isCreation = true): array
    {
        $declarationData = [
            'employeur' => $data['employeur'],
            'siret_employeur' => $data['siret_employeur'] ?? null,
            'adresse_employeur' => $data['adresse_employeur'] ?? null,
            'annee' => $data['annee'],
            'mois' => $data['mois'],
            'periode' => $this->formatPeriode($data['annee'], $data['mois']),
            'nombre_employes' => $data['nombre_employes'] ?? 0,
            'montant_total' => $data['montant_total'] ?? 0,
            'montant_cotisations_salariales' => $data['montant_cotisations_salariales'] ?? 0,
            'montant_cotisations_patronales' => $data['montant_cotisations_patronales'] ?? 0,
            'date_reception' => $data['date_reception'] ?? now()->toDateString(),
            'date_echeance' => $data['date_echeance'] ?? null,
            'observations' => $data['observations'] ?? null,
        ];

        if ($isCreation) {
            $declarationData['user_created_id'] = $user->id;
            $declarationData['statut'] = Declaration::STATUT_EN_ATTENTE;
        }

        return $declarationData;
    }

    /**
     * Gérer l'upload de fichier
     */
    private function handleFileUpload(UploadedFile $file, string $folder): string
    {
        $filename = time() . '_' . $file->getClientOriginalName();
        return $file->storeAs("declarations/{$folder}", $filename, 'public');
    }

    /**
     * Générer un numéro de déclaration unique avec gestion des conflits
     */
    private function generateUniqueNumero(): string
    {
        $annee = date('Y');

        // Approche simple et robuste : utiliser une requête SQL pour obtenir le prochain numéro
        $prochainNumero = DB::select("
            SELECT COALESCE(MAX(CAST(SUBSTRING(numero_declaration, 9) AS UNSIGNED)), 0) + 1 as prochain_numero
            FROM declarations
            WHERE numero_declaration LIKE 'DECL{$annee}%'
        ")[0]->prochain_numero;

        $numeroGenere = "DECL{$annee}" . str_pad($prochainNumero, 3, '0', STR_PAD_LEFT);

        // Double vérification pour s'assurer de l'unicité
        $maxTentatives = 10;
        $tentative = 0;

        while (Declaration::where('numero_declaration', $numeroGenere)->exists() && $tentative < $maxTentatives) {
            $prochainNumero++;
            $numeroGenere = "DECL{$annee}" . str_pad($prochainNumero, 3, '0', STR_PAD_LEFT);
            $tentative++;
        }

        return $numeroGenere;
    }

    /**
     * Formater la période
     */
    private function formatPeriode(int $annee, int $mois): string
    {
        $moisNoms = [
            1 => 'Janvier', 2 => 'Février', 3 => 'Mars', 4 => 'Avril',
            5 => 'Mai', 6 => 'Juin', 7 => 'Juillet', 8 => 'Août',
            9 => 'Septembre', 10 => 'Octobre', 11 => 'Novembre', 12 => 'Décembre'
        ];

        return ($moisNoms[$mois] ?? 'Mois inconnu') . ' ' . $annee;
    }
}
