<?php

namespace Modules\Dashboard\Controllers;

use App\Http\Controllers\BaseController;
use Modules\Dashboard\Services\DashboardService;
use Illuminate\Http\Request;
use Illuminate\View\View;

class DashboardController extends BaseController
{
    protected DashboardService $dashboardService;

    public function __construct(DashboardService $dashboardService)
    {
        $this->dashboardService = $dashboardService;
    }

    /**
     * Display the dashboard
     */
    public function index(): View
    {
        $data = [
            'stats' => $this->dashboardService->getQuickStats(),
            'recentActivities' => $this->dashboardService->getRecentActivities(),
            'chartData' => $this->dashboardService->getChartData(),
            'pendingTasks' => $this->dashboardService->getPendingTasks(),
            'notifications' => $this->dashboardService->getNotifications(),
        ];

        return view('dashboard::index', $data);
    }

    /**
     * Get chart data via AJAX
     */
    public function getChartData(Request $request)
    {
        $type = $request->get('type', 'cotisations');
        $period = $request->get('period', 'month');
        
        $data = $this->dashboardService->getChartData($type, $period);
        
        return $this->successResponse('Données récupérées avec succès', $data);
    }

    /**
     * Get widget data via AJAX
     */
    public function getWidgetData(Request $request)
    {
        $widget = $request->get('widget');
        
        $data = match($widget) {
            'stats' => $this->dashboardService->getQuickStats(),
            'activities' => $this->dashboardService->getRecentActivities(),
            'tasks' => $this->dashboardService->getPendingTasks(),
            'notifications' => $this->dashboardService->getNotifications(),
            default => []
        };
        
        return $this->successResponse('Données récupérées avec succès', $data);
    }
}
