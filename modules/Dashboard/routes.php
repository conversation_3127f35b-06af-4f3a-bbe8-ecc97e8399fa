<?php

use Illuminate\Support\Facades\Route;
use Modules\Dashboard\Controllers\DashboardController;

/*
|--------------------------------------------------------------------------
| Dashboard Module Routes
|--------------------------------------------------------------------------
*/

Route::middleware(['web', 'auth', 'permission:view-dashboard'])->group(function () {
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard.index');
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard.home');

    // AJAX routes for dashboard data
    Route::prefix('dashboard/api')->name('dashboard.api.')->group(function () {
        Route::get('/chart-data', [DashboardController::class, 'getChartData'])->name('chart-data');
        Route::get('/widget-data', [DashboardController::class, 'getWidgetData'])->name('widget-data');
    });
});