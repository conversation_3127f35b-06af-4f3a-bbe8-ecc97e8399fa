<?php

namespace Modules\Auth\Models;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Permission extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'name',
        'slug',
        'description',
        'module',
        'is_active'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Searchable fields
     */
    protected array $searchable = ['name', 'description', 'module'];

    /**
     * Permission constants
     */
    const VIEW_DASHBOARD = 'view-dashboard';
    const MANAGE_USERS = 'manage-users';
    const MANAGE_ROLES = 'manage-roles';
    const VIEW_ADHERENTS = 'view-adherents';
    const MANAGE_ADHERENTS = 'manage-adherents';
    const VIEW_COTISATIONS = 'view-cotisations';
    const MANAGE_COTISATIONS = 'manage-cotisations';
    const VIEW_PENSIONS = 'view-pensions';
    const MANAGE_PENSIONS = 'manage-pensions';
    const VIEW_REPORTS = 'view-reports';
    const GENERATE_REPORTS = 'generate-reports';

    /**
     * Get roles that have this permission
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'role_permissions')
                    ->withTimestamps();
    }

    /**
     * Scope for active permissions
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope by module
     */
    public function scopeByModule($query, string $module)
    {
        return $query->where('module', $module);
    }

    /**
     * Get permission by slug
     */
    public static function findBySlug(string $slug): ?self
    {
        return static::where('slug', $slug)->first();
    }

    /**
     * Get permissions grouped by module
     */
    public static function getGroupedByModule(): array
    {
        return static::active()
                    ->orderBy('module')
                    ->orderBy('name')
                    ->get()
                    ->groupBy('module')
                    ->toArray();
    }
}
