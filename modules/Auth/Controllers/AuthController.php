<?php

namespace Modules\Auth\Controllers;

use App\Http\Controllers\BaseController;
use Modules\Auth\Services\AuthService;
use Modules\Auth\Requests\LoginRequest;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class AuthController extends BaseController
{
    protected AuthService $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    /**
     * Show login form
     */
    public function showLoginForm(): View
    {
        return view('auth::login-template');
    }

    /**
     * Handle login request
     */
    public function login(LoginRequest $request): RedirectResponse
    {
        $credentials = $request->only('email', 'password');
        $remember = $request->boolean('remember');

        if ($this->authService->login($credentials)) {
            $request->session()->regenerate();
            
            return $this->successRedirect('dashboard.index', 'Connexion réussie !');
        }

        return back()->withErrors([
            'email' => 'Les identifiants fournis ne correspondent pas à nos enregistrements.',
        ])->onlyInput('email');
    }

    /**
     * Handle logout request
     */
    public function logout(Request $request): RedirectResponse
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('auth.login')->with('success', 'Déconnexion réussie !');
    }

    /**
     * Show forgot password form
     */
    public function showForgotPasswordForm(): View
    {
        return view('auth::forgot-password');
    }

    /**
     * Show user profile
     */
    public function showProfile(): View
    {
        $user = Auth::user();
        return view('auth::profile', compact('user'));
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . Auth::id(),
            'phone' => 'nullable|string|max:20',
            'password' => 'nullable|string|min:8|confirmed',
        ]);

        $user = Auth::user();
        $data = $request->only('name', 'email', 'phone');
        
        if ($request->filled('password')) {
            $data['password'] = $request->password;
        }

        if ($this->authService->updateUser($user, $data)) {
            return $this->backWithSuccess('Profil mis à jour avec succès !');
        }

        return $this->backWithError('Erreur lors de la mise à jour du profil.');
    }
}
