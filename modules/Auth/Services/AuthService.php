<?php

namespace Modules\Auth\Services;

use App\Models\User;
use Modules\Auth\Repositories\UserRepository;
use Modules\Auth\Models\Role;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AuthService
{
    protected UserRepository $userRepository;

    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    /**
     * Authenticate user
     */
    public function login(array $credentials): bool
    {
        if (Auth::attempt($credentials)) {
            $user = Auth::user();
            
            // Update last login
            $this->userRepository->updateLastLogin($user);
            
            // Log successful login
            Log::info('User logged in', [
                'user_id' => $user->id,
                'email' => $user->email,
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent()
            ]);
            
            return true;
        }

        // Log failed login attempt
        Log::warning('Failed login attempt', [
            'email' => $credentials['email'] ?? 'unknown',
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        return false;
    }

    /**
     * Create new user
     */
    public function createUser(array $data): User
    {
        $data['password'] = Hash::make($data['password']);
        
        $user = $this->userRepository->create($data);
        
        // Assign default role if specified
        if (isset($data['role_slug'])) {
            $role = Role::findBySlug($data['role_slug']);
            if ($role) {
                $user->assignRole($role);
            }
        }

        Log::info('User created', [
            'user_id' => $user->id,
            'email' => $user->email,
            'created_by' => Auth::id()
        ]);

        return $user;
    }

    /**
     * Update user
     */
    public function updateUser(User $user, array $data): bool
    {
        // Hash password if provided
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        } else {
            unset($data['password']);
        }

        $result = $this->userRepository->update($user, $data);

        if ($result) {
            Log::info('User updated', [
                'user_id' => $user->id,
                'email' => $user->email,
                'updated_by' => Auth::id()
            ]);
        }

        return $result;
    }

    /**
     * Deactivate user
     */
    public function deactivateUser(User $user): bool
    {
        $result = $this->userRepository->update($user, ['is_active' => false]);

        if ($result) {
            Log::info('User deactivated', [
                'user_id' => $user->id,
                'email' => $user->email,
                'deactivated_by' => Auth::id()
            ]);
        }

        return $result;
    }

    /**
     * Activate user
     */
    public function activateUser(User $user): bool
    {
        $result = $this->userRepository->update($user, ['is_active' => true]);

        if ($result) {
            Log::info('User activated', [
                'user_id' => $user->id,
                'email' => $user->email,
                'activated_by' => Auth::id()
            ]);
        }

        return $result;
    }

    /**
     * Check if user has permission
     */
    public function userHasPermission(User $user, string $permission): bool
    {
        return $user->hasPermission($permission);
    }

    /**
     * Check if user has role
     */
    public function userHasRole(User $user, string $role): bool
    {
        return $user->hasRole($role);
    }

    /**
     * Get user permissions
     */
    public function getUserPermissions(User $user): array
    {
        return $user->getAllPermissions();
    }
}
