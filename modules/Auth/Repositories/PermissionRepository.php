<?php

namespace Modules\Auth\Repositories;

use App\Repositories\BaseRepository;
use App\Contracts\RepositoryInterface;
use Modules\Auth\Models\Permission;
use Illuminate\Database\Eloquent\Collection;

class PermissionRepository extends BaseRepository implements RepositoryInterface
{
    /**
     * Constructor
     */
    public function __construct(Permission $model)
    {
        parent::__construct($model);
    }

    /**
     * Get active permissions
     */
    public function getActivePermissions(): Collection
    {
        return $this->model->active()->orderBy('module')->orderBy('name')->get();
    }

    /**
     * Get permissions by module
     */
    public function getPermissionsByModule(string $module): Collection
    {
        return $this->model->where('module', $module)->active()->get();
    }

    /**
     * Get permissions grouped by module
     */
    public function getPermissionsGroupedByModule(): array
    {
        return $this->model->active()
                          ->orderBy('module')
                          ->orderBy('name')
                          ->get()
                          ->groupBy('module')
                          ->toArray();
    }

    /**
     * Find permission by slug
     */
    public function findBySlug(string $slug): ?Permission
    {
        return $this->model->where('slug', $slug)->first();
    }
}
