<?php

namespace Modules\Auth\Repositories;

use App\Repositories\BaseRepository;
use App\Contracts\RepositoryInterface;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class UserRepository extends BaseRepository implements RepositoryInterface
{
    /**
     * Constructor
     */
    public function __construct(User $model)
    {
        parent::__construct($model);
    }

    /**
     * Get active users
     */
    public function getActiveUsers(): Collection
    {
        return $this->model->active()->get();
    }

    /**
     * Get users with roles
     */
    public function getUsersWithRoles(): Collection
    {
        return $this->model->with('roles')->get();
    }

    /**
     * Find user by email
     */
    public function findByEmail(string $email): ?User
    {
        return $this->model->where('email', $email)->first();
    }

    /**
     * Get users by role
     */
    public function getUsersByRole(string $roleSlug): Collection
    {
        return $this->model->whereHas('roles', function ($query) use ($roleSlug) {
            $query->where('slug', $roleSlug);
        })->get();
    }

    /**
     * Get paginated users with roles
     */
    public function getPaginatedUsersWithRoles(int $perPage = 15): LengthAwarePaginator
    {
        return $this->model->with('roles')
                          ->orderBy('created_at', 'desc')
                          ->paginate($perPage);
    }

    /**
     * Update last login
     */
    public function updateLastLogin(User $user): bool
    {
        return $user->update(['last_login_at' => now()]);
    }

    /**
     * Get recent users
     */
    public function getRecentUsers(int $days = 30): Collection
    {
        return $this->model->where('created_at', '>=', now()->subDays($days))
                          ->orderBy('created_at', 'desc')
                          ->get();
    }

    /**
     * Search users with roles
     */
    public function searchUsersWithRoles(string $term, int $perPage = 15): LengthAwarePaginator
    {
        return $this->model->with('roles')
                          ->where(function ($query) use ($term) {
                              $query->where('name', 'LIKE', "%{$term}%")
                                    ->orWhere('email', 'LIKE', "%{$term}%");
                          })
                          ->orderBy('created_at', 'desc')
                          ->paginate($perPage);
    }
}
