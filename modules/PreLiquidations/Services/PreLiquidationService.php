<?php

namespace Modules\PreLiquidations\Services;

use Modules\PreLiquidations\Models\PreLiquidation;
use Modules\PreLiquidations\Repositories\PreLiquidationRepository;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class PreLiquidationService
{
    protected PreLiquidationRepository $repository;

    public function __construct(PreLiquidationRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Créer un nouveau dossier de pré-liquidation
     */
    public function createDossier(array $data, User $user): PreLiquidation
    {
        try {
            DB::beginTransaction();

            // Préparer les données
            $dossierData = $this->prepareDossierData($data, $user);
            
            // Générer un numéro unique
            $dossierData['numero_dossier'] = $this->generateUniqueNumero();

            // Créer le dossier
            $preLiquidation = $this->repository->create($dossierData);

            // Log de l'action
            Log::info('Dossier de pré-liquidation créé', [
                'dossier_id' => $preLiquidation->id,
                'numero' => $preLiquidation->numero_dossier,
                'beneficiaire' => $preLiquidation->nom_complet,
                'user_id' => $user->id
            ]);

            DB::commit();
            return $preLiquidation;

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de la création du dossier de pré-liquidation', [
                'error' => $e->getMessage(),
                'data' => $data,
                'user_id' => $user->id
            ]);
            throw $e;
        }
    }

    /**
     * Mettre à jour un dossier
     */
    public function updateDossier(PreLiquidation $preLiquidation, array $data, User $user): PreLiquidation
    {
        try {
            DB::beginTransaction();

            // Vérifier si le dossier peut être modifié
            if (!$preLiquidation->peutEtreModifie()) {
                throw new Exception("Ce dossier ne peut plus être modifié (étape: {$preLiquidation->etape_label}).");
            }

            // Préparer les données
            $dossierData = $this->prepareDossierData($data, $user, false);

            // Mettre à jour
            $this->repository->update($preLiquidation, $dossierData);
            $preLiquidation->refresh();

            // Log de l'action
            Log::info('Dossier de pré-liquidation mis à jour', [
                'dossier_id' => $preLiquidation->id,
                'numero' => $preLiquidation->numero_dossier,
                'user_id' => $user->id
            ]);

            DB::commit();
            return $preLiquidation;

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de la mise à jour du dossier', [
                'error' => $e->getMessage(),
                'dossier_id' => $preLiquidation->id,
                'user_id' => $user->id
            ]);
            throw $e;
        }
    }

    /**
     * Avancer un dossier à l'étape suivante
     */
    public function avancerEtape(PreLiquidation $preLiquidation, User $user): void
    {
        try {
            DB::beginTransaction();

            $etapeActuelle = $preLiquidation->etape_actuelle;
            $preLiquidation->avancerEtape($user);

            // Effectuer des actions spécifiques selon l'étape
            $this->executerActionsEtape($preLiquidation, $user);

            Log::info('Dossier avancé à l\'étape suivante', [
                'dossier_id' => $preLiquidation->id,
                'numero' => $preLiquidation->numero_dossier,
                'etape_precedente' => $etapeActuelle,
                'etape_actuelle' => $preLiquidation->etape_actuelle,
                'user_id' => $user->id
            ]);

            DB::commit();

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de l\'avancement d\'étape', [
                'error' => $e->getMessage(),
                'dossier_id' => $preLiquidation->id,
                'user_id' => $user->id
            ]);
            throw $e;
        }
    }

    /**
     * Calculer la pension
     */
    public function calculerPension(PreLiquidation $preLiquidation, User $user): void
    {
        try {
            DB::beginTransaction();

            // Calculs de pension (logique simplifiée)
            $calculs = $this->effectuerCalculsPension($preLiquidation);

            // Mettre à jour le dossier avec les calculs
            $this->repository->update($preLiquidation, [
                'montant_pension_brute' => $calculs['brute'],
                'montant_pension_nette' => $calculs['nette'],
                'taux_pension' => $calculs['taux'],
                'coefficient_majoration' => $calculs['coefficient'],
                'etape_actuelle' => PreLiquidation::ETAPE_VALIDATION,
                'statut' => PreLiquidation::STATUT_PRET,
                'date_calcul' => now(),
                'user_assigned_id' => $user->id
            ]);

            Log::info('Pension calculée', [
                'dossier_id' => $preLiquidation->id,
                'numero' => $preLiquidation->numero_dossier,
                'montant_brut' => $calculs['brute'],
                'montant_net' => $calculs['nette'],
                'user_id' => $user->id
            ]);

            DB::commit();

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors du calcul de pension', [
                'error' => $e->getMessage(),
                'dossier_id' => $preLiquidation->id,
                'user_id' => $user->id
            ]);
            throw $e;
        }
    }

    /**
     * Valider un dossier
     */
    public function validerDossier(PreLiquidation $preLiquidation, User $user, string $observations = null): void
    {
        if (!$preLiquidation->peutEtreValide()) {
            throw new Exception("Ce dossier ne peut pas être validé (étape: {$preLiquidation->etape_label}).");
        }

        $this->repository->update($preLiquidation, [
            'etape_actuelle' => PreLiquidation::ETAPE_LIQUIDATION,
            'statut' => PreLiquidation::STATUT_LIQUIDE,
            'date_validation' => now(),
            'user_validated_id' => $user->id,
            'observations' => $observations ? 
                ($preLiquidation->observations ? $preLiquidation->observations . "\n\n" : '') . 
                "Validé le " . now()->format('d/m/Y') . " : " . $observations : 
                $preLiquidation->observations
        ]);

        Log::info('Dossier validé', [
            'dossier_id' => $preLiquidation->id,
            'numero' => $preLiquidation->numero_dossier,
            'user_id' => $user->id
        ]);
    }

    /**
     * Rejeter un dossier
     */
    public function rejeterDossier(PreLiquidation $preLiquidation, User $user, string $motif): void
    {
        $preLiquidation->rejeter($user, $motif);

        Log::info('Dossier rejeté', [
            'dossier_id' => $preLiquidation->id,
            'numero' => $preLiquidation->numero_dossier,
            'motif' => $motif,
            'user_id' => $user->id
        ]);
    }

    /**
     * Suspendre un dossier
     */
    public function suspendreDossier(PreLiquidation $preLiquidation, User $user, string $motif): void
    {
        $preLiquidation->suspendre($user, $motif);

        Log::info('Dossier suspendu', [
            'dossier_id' => $preLiquidation->id,
            'numero' => $preLiquidation->numero_dossier,
            'motif' => $motif,
            'user_id' => $user->id
        ]);
    }

    /**
     * Reprendre un dossier suspendu
     */
    public function reprendreDossier(PreLiquidation $preLiquidation, User $user): void
    {
        $preLiquidation->reprendre($user);

        Log::info('Dossier repris', [
            'dossier_id' => $preLiquidation->id,
            'numero' => $preLiquidation->numero_dossier,
            'user_id' => $user->id
        ]);
    }

    /**
     * Supprimer un dossier
     */
    public function deleteDossier(PreLiquidation $preLiquidation, User $user): void
    {
        try {
            DB::beginTransaction();

            // Supprimer le dossier (soft delete)
            $this->repository->delete($preLiquidation);

            Log::info('Dossier supprimé', [
                'dossier_id' => $preLiquidation->id,
                'numero' => $preLiquidation->numero_dossier,
                'user_id' => $user->id
            ]);

            DB::commit();

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de la suppression du dossier', [
                'error' => $e->getMessage(),
                'dossier_id' => $preLiquidation->id,
                'user_id' => $user->id
            ]);
            throw $e;
        }
    }

    /**
     * Obtenir les statistiques
     */
    public function getStatistics(): array
    {
        return $this->repository->getStatistics();
    }

    /**
     * Préparer les données du dossier
     */
    private function prepareDossierData(array $data, User $user, bool $isCreation = true): array
    {
        $dossierData = [
            'nom_beneficiaire' => $data['nom_beneficiaire'],
            'prenom_beneficiaire' => $data['prenom_beneficiaire'],
            'date_naissance' => $data['date_naissance'],
            'lieu_naissance' => $data['lieu_naissance'] ?? null,
            'sexe' => $data['sexe'],
            'numero_securite_sociale' => $data['numero_securite_sociale'] ?? null,
            'numero_adherent' => $data['numero_adherent'] ?? null,
            'adresse' => $data['adresse'] ?? null,
            'telephone' => $data['telephone'] ?? null,
            'email' => $data['email'] ?? null,
            'type_pension' => $data['type_pension'],
            'date_depart_retraite' => $data['date_depart_retraite'],
            'date_fin_activite' => $data['date_fin_activite'] ?? null,
            'dernier_employeur' => $data['dernier_employeur'] ?? null,
            'fonction' => $data['fonction'] ?? null,
            'duree_cotisation_mois' => $data['duree_cotisation_mois'] ?? 0,
            'salaire_reference' => $data['salaire_reference'] ?? null,
            'priorite' => $data['priorite'] ?? PreLiquidation::PRIORITE_NORMALE,
            'observations' => $data['observations'] ?? null,
        ];

        if ($isCreation) {
            $dossierData['user_created_id'] = $user->id;
            $dossierData['user_assigned_id'] = $user->id;
            $dossierData['etape_actuelle'] = PreLiquidation::ETAPE_RECEPTION;
            $dossierData['statut'] = PreLiquidation::STATUT_NOUVEAU;
            $dossierData['date_reception'] = now();
        }

        return $dossierData;
    }

    /**
     * Générer un numéro de dossier unique
     */
    private function generateUniqueNumero(): string
    {
        $maxTentatives = 10;
        
        for ($i = 0; $i < $maxTentatives; $i++) {
            $numero = PreLiquidation::generateNumeroDossier();
            
            if (!$this->repository->numeroDossierExiste($numero)) {
                return $numero;
            }
            
            usleep(rand(1000, 5000)); // 1-5ms aléatoire
        }

        // En dernier recours, utiliser un UUID court
        return "PL" . date('Y') . strtoupper(substr(uniqid(), -4));
    }

    /**
     * Effectuer les calculs de pension (logique simplifiée)
     */
    private function effectuerCalculsPension(PreLiquidation $preLiquidation): array
    {
        // Logique de calcul simplifiée - à adapter selon les règles métier
        $salaireReference = $preLiquidation->salaire_reference ?? 0;
        $dureeCotisationAnnees = $preLiquidation->duree_cotisation_mois / 12;
        
        // Taux de pension basé sur la durée de cotisation
        $taux = min(75, $dureeCotisationAnnees * 1.875); // Max 75%
        
        // Coefficient de majoration selon l'âge
        $age = $preLiquidation->age;
        $coefficient = 1.0;
        if ($age >= 65) {
            $coefficient = 1.05; // Majoration de 5% après 65 ans
        }
        
        // Calcul du montant brut
        $montantBrut = ($salaireReference * $taux / 100) * $coefficient;
        
        // Calcul du montant net (déduction forfaitaire de 20%)
        $montantNet = $montantBrut * 0.8;
        
        return [
            'brute' => round($montantBrut, 2),
            'nette' => round($montantNet, 2),
            'taux' => round($taux, 2),
            'coefficient' => round($coefficient, 4)
        ];
    }

    /**
     * Exécuter des actions spécifiques selon l'étape
     */
    private function executerActionsEtape(PreLiquidation $preLiquidation, User $user): void
    {
        switch ($preLiquidation->etape_actuelle) {
            case PreLiquidation::ETAPE_VERIFICATION:
                $preLiquidation->update(['statut' => PreLiquidation::STATUT_EN_COURS]);
                break;
                
            case PreLiquidation::ETAPE_CALCUL:
                // Auto-calcul si les données sont complètes
                if ($preLiquidation->salaire_reference && $preLiquidation->duree_cotisation_mois > 0) {
                    $this->calculerPension($preLiquidation, $user);
                }
                break;
                
            case PreLiquidation::ETAPE_TERMINE:
                $preLiquidation->update(['statut' => PreLiquidation::STATUT_PAYE]);
                break;
        }
    }
}
