<?php

namespace Modules\PreLiquidations\Repositories;

use Modules\PreLiquidations\Models\PreLiquidation;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class PreLiquidationRepository
{
    protected PreLiquidation $model;

    public function __construct(PreLiquidation $model)
    {
        $this->model = $model;
    }

    /**
     * Récupérer tous les dossiers avec pagination et filtres
     */
    public function getAllWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery()
            ->with(['userCreated', 'userAssigned', 'userValidated'])
            ->orderBy('priorite', 'desc')
            ->orderBy('date_depart_retraite', 'asc')
            ->orderBy('created_at', 'desc');

        // Filtre par étape
        if (!empty($filters['etape'])) {
            $query->parEtape($filters['etape']);
        }

        // Filtre par statut
        if (!empty($filters['statut'])) {
            $query->parStatut($filters['statut']);
        }

        // Filtre par type de pension
        if (!empty($filters['type_pension'])) {
            $query->parTypePension($filters['type_pension']);
        }

        // Filtre par priorité
        if (!empty($filters['priorite'])) {
            $query->parPriorite($filters['priorite']);
        }

        // Filtre par utilisateur assigné
        if (!empty($filters['user_assigned'])) {
            $query->assigneA($filters['user_assigned']);
        }

        // Filtre par date de départ retraite
        if (!empty($filters['date_depart_debut'])) {
            $query->where('date_depart_retraite', '>=', $filters['date_depart_debut']);
        }

        if (!empty($filters['date_depart_fin'])) {
            $query->where('date_depart_retraite', '<=', $filters['date_depart_fin']);
        }

        // Recherche textuelle
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('numero_dossier', 'like', "%{$search}%")
                  ->orWhere('nom_beneficiaire', 'like', "%{$search}%")
                  ->orWhere('prenom_beneficiaire', 'like', "%{$search}%")
                  ->orWhere('numero_adherent', 'like', "%{$search}%")
                  ->orWhere('numero_securite_sociale', 'like', "%{$search}%");
            });
        }

        // Filtre dossiers urgents
        if (!empty($filters['urgents'])) {
            $query->urgents();
        }

        // Filtre dossiers en retard
        if (!empty($filters['en_retard'])) {
            $query->enRetard();
        }

        return $query->paginate($perPage);
    }

    /**
     * Trouver un dossier par ID avec relations
     */
    public function findWithRelations(int $id): ?PreLiquidation
    {
        return $this->model->with([
            'userCreated',
            'userAssigned', 
            'userValidated'
        ])->find($id);
    }

    /**
     * Créer un nouveau dossier
     */
    public function create(array $data): PreLiquidation
    {
        return $this->model->create($data);
    }

    /**
     * Mettre à jour un dossier
     */
    public function update(PreLiquidation $preLiquidation, array $data): bool
    {
        return $preLiquidation->update($data);
    }

    /**
     * Supprimer un dossier (soft delete)
     */
    public function delete(PreLiquidation $preLiquidation): bool
    {
        return $preLiquidation->delete();
    }

    /**
     * Statistiques des dossiers
     */
    public function getStatistics(): array
    {
        $stats = $this->model->selectRaw('
            COUNT(*) as total,
            COUNT(CASE WHEN etape_actuelle = "reception" THEN 1 END) as reception,
            COUNT(CASE WHEN etape_actuelle = "verification" THEN 1 END) as verification,
            COUNT(CASE WHEN etape_actuelle = "calcul" THEN 1 END) as calcul,
            COUNT(CASE WHEN etape_actuelle = "validation" THEN 1 END) as validation,
            COUNT(CASE WHEN etape_actuelle = "liquidation" THEN 1 END) as liquidation,
            COUNT(CASE WHEN etape_actuelle = "paiement" THEN 1 END) as paiement,
            COUNT(CASE WHEN etape_actuelle = "termine" THEN 1 END) as termines,
            COUNT(CASE WHEN etape_actuelle = "rejete" THEN 1 END) as rejetes,
            COUNT(CASE WHEN priorite = "urgente" THEN 1 END) as urgents,
            COUNT(CASE WHEN date_depart_retraite < CURDATE() AND etape_actuelle NOT IN ("termine", "rejete") THEN 1 END) as en_retard,
            SUM(CASE WHEN montant_pension_nette IS NOT NULL THEN montant_pension_nette ELSE 0 END) as montant_total_pensions
        ')->first();

        return [
            'total' => $stats->total ?? 0,
            'reception' => $stats->reception ?? 0,
            'verification' => $stats->verification ?? 0,
            'calcul' => $stats->calcul ?? 0,
            'validation' => $stats->validation ?? 0,
            'liquidation' => $stats->liquidation ?? 0,
            'paiement' => $stats->paiement ?? 0,
            'termines' => $stats->termines ?? 0,
            'rejetes' => $stats->rejetes ?? 0,
            'urgents' => $stats->urgents ?? 0,
            'en_retard' => $stats->en_retard ?? 0,
            'montant_total_pensions' => $stats->montant_total_pensions ?? 0,
        ];
    }

    /**
     * Dossiers récents
     */
    public function getRecents(int $limit = 10): Collection
    {
        return $this->model->with(['userCreated'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Dossiers urgents
     */
    public function getUrgents(): Collection
    {
        return $this->model->urgents()
            ->with(['userAssigned'])
            ->orderBy('date_depart_retraite', 'asc')
            ->get();
    }

    /**
     * Dossiers en retard
     */
    public function getEnRetard(): Collection
    {
        return $this->model->enRetard()
            ->with(['userAssigned'])
            ->orderBy('date_depart_retraite', 'asc')
            ->get();
    }

    /**
     * Dossiers par étape
     */
    public function getParEtape(string $etape): Collection
    {
        return $this->model->parEtape($etape)
            ->with(['userAssigned'])
            ->orderBy('priorite', 'desc')
            ->orderBy('date_depart_retraite', 'asc')
            ->get();
    }

    /**
     * Dossiers assignés à un utilisateur
     */
    public function getAssignesA(int $userId): Collection
    {
        return $this->model->assigneA($userId)
            ->orderBy('priorite', 'desc')
            ->orderBy('date_depart_retraite', 'asc')
            ->get();
    }

    /**
     * Recherche avancée
     */
    public function search(string $term): Collection
    {
        return $this->model->where(function ($query) use ($term) {
            $query->where('numero_dossier', 'like', "%{$term}%")
                  ->orWhere('nom_beneficiaire', 'like', "%{$term}%")
                  ->orWhere('prenom_beneficiaire', 'like', "%{$term}%")
                  ->orWhere('numero_adherent', 'like', "%{$term}%")
                  ->orWhere('numero_securite_sociale', 'like', "%{$term}%");
        })
        ->with(['userAssigned'])
        ->orderBy('priorite', 'desc')
        ->orderBy('created_at', 'desc')
        ->limit(20)
        ->get();
    }

    /**
     * Évolution des dossiers par mois
     */
    public function getEvolutionParMois(int $annee = null): Collection
    {
        $annee = $annee ?? date('Y');
        
        return $this->model->selectRaw('
            MONTH(created_at) as mois,
            COUNT(*) as nombre_crees,
            COUNT(CASE WHEN etape_actuelle = "termine" THEN 1 END) as nombre_termines,
            AVG(CASE WHEN montant_pension_nette IS NOT NULL THEN montant_pension_nette END) as montant_moyen
        ')
        ->whereYear('created_at', $annee)
        ->groupBy(DB::raw('MONTH(created_at)'))
        ->orderBy('mois')
        ->get();
    }

    /**
     * Top types de pension
     */
    public function getTopTypesPension(): Collection
    {
        return $this->model->selectRaw('
            type_pension,
            COUNT(*) as nombre,
            AVG(CASE WHEN montant_pension_nette IS NOT NULL THEN montant_pension_nette END) as montant_moyen
        ')
        ->groupBy('type_pension')
        ->orderBy('nombre', 'desc')
        ->get();
    }

    /**
     * Vérifier si un numéro de dossier existe
     */
    public function numeroDossierExiste(string $numero, int $excludeId = null): bool
    {
        $query = $this->model->where('numero_dossier', $numero);
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        
        return $query->exists();
    }
}
