@extends('layouts.template')

@section('title', 'Pré-liquidations - CRFM')

@section('page-header')
@section('page-title', 'Gestion des Pré-liquidations')
@section('page-description', 'Traitement des dossiers de pré-liquidation de pension')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Dossiers</a></li>
        <li class="breadcrumb-item"><a href="#!">Pré-liquidations</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <!-- Statistiques rapides -->
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-blue text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($statistics['total'] ?? 0) }}</h4>
                        <h6 class="text-white m-b-0">Total dossiers</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-folder f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-yellow text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($statistics['urgents'] ?? 0) }}</h4>
                        <h6 class="text-white m-b-0">Urgents</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-alert-triangle f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-red text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600" style="color: white !important;">{{ number_format($statistics['en_retard'] ?? 0) }}</h4>
                        <h6 class="text-white m-b-0" style="color: white !important;">En retard</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-clock f-28 text-white" style="color: white !important;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-green text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($statistics['termines'] ?? 0) }}</h4>
                        <h6 class="text-white m-b-0">Terminés</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-check-circle f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Filtres et recherche -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-filter text-c-blue me-2"></i>Filtres et recherche</h5>
                <div class="card-header-right">
                    <a href="{{ route('pre-liquidations.create') }}" class="btn btn-success btn-sm">
                        <i class="feather icon-plus me-1"></i>Nouveau dossier
                    </a>
                </div>
            </div>
            <div class="card-block">
                <form method="GET" action="{{ route('pre-liquidations.index') }}">
                    <div class="row">
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Étape</label>
                                <select name="etape" class="form-control">
                                    <option value="">Toutes les étapes</option>
                                    <option value="reception" {{ ($filters['etape'] ?? '') === 'reception' ? 'selected' : '' }}>Réception</option>
                                    <option value="verification" {{ ($filters['etape'] ?? '') === 'verification' ? 'selected' : '' }}>Vérification</option>
                                    <option value="calcul" {{ ($filters['etape'] ?? '') === 'calcul' ? 'selected' : '' }}>Calcul</option>
                                    <option value="validation" {{ ($filters['etape'] ?? '') === 'validation' ? 'selected' : '' }}>Validation</option>
                                    <option value="liquidation" {{ ($filters['etape'] ?? '') === 'liquidation' ? 'selected' : '' }}>Liquidation</option>
                                    <option value="paiement" {{ ($filters['etape'] ?? '') === 'paiement' ? 'selected' : '' }}>Paiement</option>
                                    <option value="termine" {{ ($filters['etape'] ?? '') === 'termine' ? 'selected' : '' }}>Terminé</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Statut</label>
                                <select name="statut" class="form-control">
                                    <option value="">Tous les statuts</option>
                                    <option value="nouveau" {{ ($filters['statut'] ?? '') === 'nouveau' ? 'selected' : '' }}>Nouveau</option>
                                    <option value="en_cours" {{ ($filters['statut'] ?? '') === 'en_cours' ? 'selected' : '' }}>En cours</option>
                                    <option value="pret" {{ ($filters['statut'] ?? '') === 'pret' ? 'selected' : '' }}>Prêt</option>
                                    <option value="liquide" {{ ($filters['statut'] ?? '') === 'liquide' ? 'selected' : '' }}>Liquidé</option>
                                    <option value="paye" {{ ($filters['statut'] ?? '') === 'paye' ? 'selected' : '' }}>Payé</option>
                                    <option value="suspendu" {{ ($filters['statut'] ?? '') === 'suspendu' ? 'selected' : '' }}>Suspendu</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Type pension</label>
                                <select name="type_pension" class="form-control">
                                    <option value="">Tous les types</option>
                                    <option value="vieillesse" {{ ($filters['type_pension'] ?? '') === 'vieillesse' ? 'selected' : '' }}>Vieillesse</option>
                                    <option value="anticipee" {{ ($filters['type_pension'] ?? '') === 'anticipee' ? 'selected' : '' }}>Anticipée</option>
                                    <option value="invalidite" {{ ($filters['type_pension'] ?? '') === 'invalidite' ? 'selected' : '' }}>Invalidité</option>
                                    <option value="survivant" {{ ($filters['type_pension'] ?? '') === 'survivant' ? 'selected' : '' }}>Survivant</option>
                                    <option value="orphelin" {{ ($filters['type_pension'] ?? '') === 'orphelin' ? 'selected' : '' }}>Orphelin</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Priorité</label>
                                <select name="priorite" class="form-control">
                                    <option value="">Toutes</option>
                                    <option value="normale" {{ ($filters['priorite'] ?? '') === 'normale' ? 'selected' : '' }}>Normale</option>
                                    <option value="haute" {{ ($filters['priorite'] ?? '') === 'haute' ? 'selected' : '' }}>Haute</option>
                                    <option value="urgente" {{ ($filters['priorite'] ?? '') === 'urgente' ? 'selected' : '' }}>Urgente</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Recherche</label>
                                <div class="input-group">
                                    <input type="text" name="search" class="form-control" placeholder="N° dossier, nom, prénom..." value="{{ $filters['search'] ?? '' }}">
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="feather icon-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" name="urgents" value="1" {{ !empty($filters['urgents']) ? 'checked' : '' }}>
                                <label class="form-check-label">Dossiers urgents</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" name="en_retard" value="1" {{ !empty($filters['en_retard']) ? 'checked' : '' }}>
                                <label class="form-check-label">Dossiers en retard</label>
                            </div>
                            <a href="{{ route('pre-liquidations.index') }}" class="btn btn-secondary btn-sm ms-3">
                                <i class="feather icon-refresh-cw me-1"></i>Réinitialiser
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Liste des dossiers -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-list text-c-blue me-2"></i>Dossiers de pré-liquidation ({{ $dossiers->total() }})</h5>
            </div>
            <div class="card-block">
                @if($dossiers->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>N° Dossier</th>
                                    <th>Bénéficiaire</th>
                                    <th>Type Pension</th>
                                    <th>Date Départ</th>
                                    <th>Étape</th>
                                    <th>Statut</th>
                                    <th>Priorité</th>
                                    <th>Montant</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($dossiers as $dossier)
                                    <tr class="{{ $dossier->priorite === 'urgente' ? 'table-danger' : ($dossier->priorite === 'haute' ? 'table-warning' : '') }}">
                                        <td>
                                            <strong>{{ $dossier->numero_dossier }}</strong>
                                            @if($dossier->date_depart_retraite < now() && !in_array($dossier->etape_actuelle, ['termine', 'rejete']))
                                                <i class="feather icon-clock text-danger ms-1" title="En retard"></i>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="d-inline-block align-middle">
                                                <h6 class="m-b-0">{{ $dossier->nom_complet }}</h6>
                                                <p class="m-b-0 text-muted">{{ $dossier->age }} ans</p>
                                            </div>
                                        </td>
                                        <td>{{ $dossier->type_pension_label }}</td>
                                        <td>{{ $dossier->date_depart_retraite->format('d/m/Y') }}</td>
                                        <td>
                                            <span class="badge bg-{{ $dossier->etape_color }}">
                                                {{ $dossier->etape_label }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $dossier->statut_color }}">
                                                {{ $dossier->statut_label }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $dossier->priorite_color }}">
                                                {{ $dossier->priorite_label }}
                                            </span>
                                        </td>
                                        <td>{{ $dossier->montant_pension_formate }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('pre-liquidations.show', $dossier) }}" class="btn btn-sm btn-primary" title="Voir">
                                                    <i class="feather icon-eye"></i>
                                                </a>
                                                @if($dossier->peutEtreModifie())
                                                    <a href="{{ route('pre-liquidations.edit', $dossier) }}" class="btn btn-sm btn-warning" title="Modifier">
                                                        <i class="feather icon-edit"></i>
                                                    </a>
                                                @endif
                                                <a href="{{ route('pre-liquidations.workflow', $dossier) }}" class="btn btn-sm btn-info" title="Workflow">
                                                    <i class="feather icon-git-branch"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-secondary" onclick="supprimerDossier({{ $dossier->id }})" title="Supprimer">
                                                    <i class="feather icon-trash-2"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            Affichage de {{ $dossiers->firstItem() }} à {{ $dossiers->lastItem() }} 
                            sur {{ $dossiers->total() }} résultats
                        </div>
                        <div>
                            {{ $dossiers->appends(request()->query())->links() }}
                        </div>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="feather icon-folder f-40 text-muted"></i>
                        <h5 class="mt-3">Aucun dossier trouvé</h5>
                        <p class="text-muted">Aucun dossier ne correspond aux critères de recherche.</p>
                        <a href="{{ route('pre-liquidations.create') }}" class="btn btn-primary">
                            <i class="feather icon-plus me-2"></i>Créer un dossier
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@if($urgents->count() > 0 || $enRetard->count() > 0)
<div class="row">
    @if($urgents->count() > 0)
    <!-- Dossiers urgents -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-alert-triangle text-c-red me-2"></i>Dossiers urgents</h5>
            </div>
            <div class="card-block">
                @foreach($urgents->take(5) as $dossier)
                    <div class="row align-items-center m-b-15">
                        <div class="col-8">
                            <h6 class="m-b-5">{{ $dossier->nom_complet }}</h6>
                            <p class="text-muted m-b-0">{{ $dossier->numero_dossier }} • {{ $dossier->etape_label }}</p>
                        </div>
                        <div class="col-4 text-end">
                            <small class="text-muted">{{ $dossier->date_depart_retraite->format('d/m/Y') }}</small>
                        </div>
                    </div>
                @endforeach
                @if($urgents->count() > 5)
                    <div class="text-center">
                        <a href="{{ route('pre-liquidations.index', ['urgents' => 1]) }}" class="btn btn-sm btn-outline-primary">
                            Voir tous ({{ $urgents->count() }})
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
    @endif

    @if($enRetard->count() > 0)
    <!-- Dossiers en retard -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-clock text-c-red me-2"></i>Dossiers en retard</h5>
            </div>
            <div class="card-block">
                @foreach($enRetard->take(5) as $dossier)
                    <div class="row align-items-center m-b-15">
                        <div class="col-8">
                            <h6 class="m-b-5">{{ $dossier->nom_complet }}</h6>
                            <p class="text-muted m-b-0">{{ $dossier->numero_dossier }} • {{ $dossier->etape_label }}</p>
                        </div>
                        <div class="col-4 text-end">
                            <small class="text-danger">{{ $dossier->date_depart_retraite->format('d/m/Y') }}</small>
                        </div>
                    </div>
                @endforeach
                @if($enRetard->count() > 5)
                    <div class="text-center">
                        <a href="{{ route('pre-liquidations.index', ['en_retard' => 1]) }}" class="btn btn-sm btn-outline-primary">
                            Voir tous ({{ $enRetard->count() }})
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
    @endif
</div>
@endif
@endsection

@push('styles')
<style>
.bg-c-red {
    background: linear-gradient(45deg, #dc3545, #c82333) !important;
}

.bg-c-red .text-white,
.bg-c-red h4,
.bg-c-red h6,
.bg-c-red i {
    color: #ffffff !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.card.bg-c-red {
    border: none;
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}
</style>
@endpush

@push('scripts')
<script>
function supprimerDossier(dossierId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce dossier ? Cette action est irréversible.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/pre-liquidations/${dossierId}`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';

        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';

        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
