@extends('layouts.template')

@section('title', 'Nouveau Dossier Pré-liquidation - CRFM')

@section('page-header')
@section('page-title', 'Nouveau Dossier de Pré-liquidation')
@section('page-description', 'Créer un nouveau dossier de pré-liquidation de pension')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Dossiers</a></li>
        <li class="breadcrumb-item"><a href="{{ route('pre-liquidations.index') }}">Pré-liquidations</a></li>
        <li class="breadcrumb-item"><a href="#!">Nouveau</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-plus text-c-green me-2"></i>Nouveau Dossier de Pré-liquidation</h5>
                <div class="card-header-right">
                    <a href="{{ route('pre-liquidations.index') }}" class="btn btn-secondary btn-sm">
                        <i class="feather icon-arrow-left me-1"></i>Retour à la liste
                    </a>
                </div>
            </div>
            <div class="card-block">
                <form action="{{ route('pre-liquidations.store') }}" method="POST">
                    @csrf
                    
                    <!-- Informations du bénéficiaire -->
                    <div class="row">
                        <div class="col-md-12">
                            <h6 class="text-muted mb-3"><i class="feather icon-user me-2"></i>Informations du bénéficiaire</h6>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Nom <span class="text-danger">*</span></label>
                                <input type="text" name="nom_beneficiaire" class="form-control {{ $errors->has('nom_beneficiaire') ? 'is-invalid' : '' }}" 
                                       value="{{ old('nom_beneficiaire') }}" placeholder="Nom du bénéficiaire" required>
                                @if($errors->has('nom_beneficiaire'))
                                    <div class="invalid-feedback">{{ $errors->first('nom_beneficiaire') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Prénom <span class="text-danger">*</span></label>
                                <input type="text" name="prenom_beneficiaire" class="form-control {{ $errors->has('prenom_beneficiaire') ? 'is-invalid' : '' }}" 
                                       value="{{ old('prenom_beneficiaire') }}" placeholder="Prénom du bénéficiaire" required>
                                @if($errors->has('prenom_beneficiaire'))
                                    <div class="invalid-feedback">{{ $errors->first('prenom_beneficiaire') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Sexe <span class="text-danger">*</span></label>
                                <select name="sexe" class="form-control {{ $errors->has('sexe') ? 'is-invalid' : '' }}" required>
                                    <option value="">Sélectionner</option>
                                    <option value="M" {{ old('sexe') === 'M' ? 'selected' : '' }}>Masculin</option>
                                    <option value="F" {{ old('sexe') === 'F' ? 'selected' : '' }}>Féminin</option>
                                </select>
                                @if($errors->has('sexe'))
                                    <div class="invalid-feedback">{{ $errors->first('sexe') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Date de naissance <span class="text-danger">*</span></label>
                                <input type="date" name="date_naissance" class="form-control {{ $errors->has('date_naissance') ? 'is-invalid' : '' }}" 
                                       value="{{ old('date_naissance') }}" required>
                                @if($errors->has('date_naissance'))
                                    <div class="invalid-feedback">{{ $errors->first('date_naissance') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Lieu de naissance</label>
                                <input type="text" name="lieu_naissance" class="form-control {{ $errors->has('lieu_naissance') ? 'is-invalid' : '' }}" 
                                       value="{{ old('lieu_naissance') }}" placeholder="Lieu de naissance">
                                @if($errors->has('lieu_naissance'))
                                    <div class="invalid-feedback">{{ $errors->first('lieu_naissance') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">N° Sécurité Sociale</label>
                                <input type="text" name="numero_securite_sociale" class="form-control {{ $errors->has('numero_securite_sociale') ? 'is-invalid' : '' }}" 
                                       value="{{ old('numero_securite_sociale') }}" placeholder="15 chiffres">
                                @if($errors->has('numero_securite_sociale'))
                                    <div class="invalid-feedback">{{ $errors->first('numero_securite_sociale') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">N° Adhérent</label>
                                <input type="text" name="numero_adherent" class="form-control {{ $errors->has('numero_adherent') ? 'is-invalid' : '' }}" 
                                       value="{{ old('numero_adherent') }}" placeholder="Numéro d'adhérent">
                                @if($errors->has('numero_adherent'))
                                    <div class="invalid-feedback">{{ $errors->first('numero_adherent') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Téléphone</label>
                                <input type="tel" name="telephone" class="form-control {{ $errors->has('telephone') ? 'is-invalid' : '' }}" 
                                       value="{{ old('telephone') }}" placeholder="Numéro de téléphone">
                                @if($errors->has('telephone'))
                                    <div class="invalid-feedback">{{ $errors->first('telephone') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Email</label>
                                <input type="email" name="email" class="form-control {{ $errors->has('email') ? 'is-invalid' : '' }}" 
                                       value="{{ old('email') }}" placeholder="Adresse email">
                                @if($errors->has('email'))
                                    <div class="invalid-feedback">{{ $errors->first('email') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Adresse</label>
                                <textarea name="adresse" class="form-control {{ $errors->has('adresse') ? 'is-invalid' : '' }}" 
                                          rows="2" placeholder="Adresse complète">{{ old('adresse') }}</textarea>
                                @if($errors->has('adresse'))
                                    <div class="invalid-feedback">{{ $errors->first('adresse') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Informations de la pension -->
                    <div class="row">
                        <div class="col-md-12">
                            <h6 class="text-muted mb-3"><i class="feather icon-award me-2"></i>Informations de la pension</h6>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Type de pension <span class="text-danger">*</span></label>
                                <select name="type_pension" class="form-control {{ $errors->has('type_pension') ? 'is-invalid' : '' }}" required>
                                    <option value="">Sélectionner un type</option>
                                    <option value="vieillesse" {{ old('type_pension') === 'vieillesse' ? 'selected' : '' }}>Pension de vieillesse</option>
                                    <option value="anticipee" {{ old('type_pension') === 'anticipee' ? 'selected' : '' }}>Pension anticipée</option>
                                    <option value="invalidite" {{ old('type_pension') === 'invalidite' ? 'selected' : '' }}>Pension d'invalidité</option>
                                    <option value="survivant" {{ old('type_pension') === 'survivant' ? 'selected' : '' }}>Pension de survivant</option>
                                    <option value="orphelin" {{ old('type_pension') === 'orphelin' ? 'selected' : '' }}>Pension d'orphelin</option>
                                </select>
                                @if($errors->has('type_pension'))
                                    <div class="invalid-feedback">{{ $errors->first('type_pension') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Date de départ retraite <span class="text-danger">*</span></label>
                                <input type="date" name="date_depart_retraite" class="form-control {{ $errors->has('date_depart_retraite') ? 'is-invalid' : '' }}" 
                                       value="{{ old('date_depart_retraite') }}" required>
                                @if($errors->has('date_depart_retraite'))
                                    <div class="invalid-feedback">{{ $errors->first('date_depart_retraite') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Date fin d'activité</label>
                                <input type="date" name="date_fin_activite" class="form-control {{ $errors->has('date_fin_activite') ? 'is-invalid' : '' }}" 
                                       value="{{ old('date_fin_activite') }}">
                                @if($errors->has('date_fin_activite'))
                                    <div class="invalid-feedback">{{ $errors->first('date_fin_activite') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Informations professionnelles -->
                    <div class="row">
                        <div class="col-md-12">
                            <h6 class="text-muted mb-3"><i class="feather icon-briefcase me-2"></i>Informations professionnelles</h6>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Dernier employeur</label>
                                <input type="text" name="dernier_employeur" class="form-control {{ $errors->has('dernier_employeur') ? 'is-invalid' : '' }}" 
                                       value="{{ old('dernier_employeur') }}" placeholder="Nom du dernier employeur">
                                @if($errors->has('dernier_employeur'))
                                    <div class="invalid-feedback">{{ $errors->first('dernier_employeur') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Fonction</label>
                                <input type="text" name="fonction" class="form-control {{ $errors->has('fonction') ? 'is-invalid' : '' }}" 
                                       value="{{ old('fonction') }}" placeholder="Dernière fonction occupée">
                                @if($errors->has('fonction'))
                                    <div class="invalid-feedback">{{ $errors->first('fonction') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Durée de cotisation (mois)</label>
                                <input type="number" name="duree_cotisation_mois" class="form-control {{ $errors->has('duree_cotisation_mois') ? 'is-invalid' : '' }}" 
                                       value="{{ old('duree_cotisation_mois') }}" min="0" placeholder="Nombre de mois">
                                @if($errors->has('duree_cotisation_mois'))
                                    <div class="invalid-feedback">{{ $errors->first('duree_cotisation_mois') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Salaire de référence (€)</label>
                                <input type="number" name="salaire_reference" class="form-control {{ $errors->has('salaire_reference') ? 'is-invalid' : '' }}" 
                                       value="{{ old('salaire_reference') }}" min="0" step="0.01" placeholder="0.00">
                                @if($errors->has('salaire_reference'))
                                    <div class="invalid-feedback">{{ $errors->first('salaire_reference') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Priorité et observations -->
                    <div class="row">
                        <div class="col-md-12">
                            <h6 class="text-muted mb-3"><i class="feather icon-settings me-2"></i>Paramètres du dossier</h6>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Priorité</label>
                                <select name="priorite" class="form-control {{ $errors->has('priorite') ? 'is-invalid' : '' }}">
                                    <option value="normale" {{ old('priorite', 'normale') === 'normale' ? 'selected' : '' }}>Normale</option>
                                    <option value="haute" {{ old('priorite') === 'haute' ? 'selected' : '' }}>Haute</option>
                                    <option value="urgente" {{ old('priorite') === 'urgente' ? 'selected' : '' }}>Urgente</option>
                                </select>
                                @if($errors->has('priorite'))
                                    <div class="invalid-feedback">{{ $errors->first('priorite') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Observations</label>
                                <textarea name="observations" class="form-control {{ $errors->has('observations') ? 'is-invalid' : '' }}" 
                                          rows="3" placeholder="Observations sur le dossier...">{{ old('observations') }}</textarea>
                                @if($errors->has('observations'))
                                    <div class="invalid-feedback">{{ $errors->first('observations') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Boutons d'action -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group text-end">
                                <a href="{{ route('pre-liquidations.index') }}" class="btn btn-secondary me-2">
                                    <i class="feather icon-x me-1"></i>Annuler
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="feather icon-save me-1"></i>Créer le dossier
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Validation côté client
document.addEventListener('DOMContentLoaded', function() {
    const dateNaissance = document.querySelector('input[name="date_naissance"]');
    const dateDepartRetraite = document.querySelector('input[name="date_depart_retraite"]');
    const dateFin = document.querySelector('input[name="date_fin_activite"]');
    const typePension = document.querySelector('select[name="type_pension"]');
    
    function validateDates() {
        if (dateNaissance.value && dateDepartRetraite.value) {
            const naissance = new Date(dateNaissance.value);
            const depart = new Date(dateDepartRetraite.value);
            const age = Math.floor((depart - naissance) / (365.25 * 24 * 60 * 60 * 1000));
            
            // Afficher l'âge calculé
            const ageDisplay = document.getElementById('age-display');
            if (ageDisplay) {
                ageDisplay.textContent = `Âge au départ: ${age} ans`;
            } else {
                const ageSpan = document.createElement('small');
                ageSpan.id = 'age-display';
                ageSpan.className = 'text-muted';
                ageSpan.textContent = `Âge au départ: ${age} ans`;
                dateDepartRetraite.parentNode.appendChild(ageSpan);
            }
        }
    }
    
    function validateDateFin() {
        if (dateFin.value && dateDepartRetraite.value) {
            const fin = new Date(dateFin.value);
            const depart = new Date(dateDepartRetraite.value);
            
            if (fin > depart) {
                dateFin.setCustomValidity('La date de fin d\'activité ne peut pas être postérieure à la date de départ.');
            } else {
                dateFin.setCustomValidity('');
            }
        }
    }
    
    dateNaissance.addEventListener('change', validateDates);
    dateDepartRetraite.addEventListener('change', function() {
        validateDates();
        validateDateFin();
    });
    dateFin.addEventListener('change', validateDateFin);
    
    // Format numéro sécurité sociale
    const numSecu = document.querySelector('input[name="numero_securite_sociale"]');
    numSecu.addEventListener('input', function() {
        this.value = this.value.replace(/\D/g, '').substring(0, 15);
    });
});
</script>
@endpush
