@extends('layouts.template')

@section('title', 'Workflow ' . $dossier->numero_dossier . ' - CRFM')

@section('page-header')
@section('page-title', 'Workflow de Pré-liquidation')
@section('page-description', 'Suivi du workflow pour le dossier ' . $dossier->numero_dossier)
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Dossiers</a></li>
        <li class="breadcrumb-item"><a href="{{ route('pre-liquidations.index') }}">Pré-liquidations</a></li>
        <li class="breadcrumb-item"><a href="{{ route('pre-liquidations.show', $dossier) }}">{{ $dossier->numero_dossier }}</a></li>
        <li class="breadcrumb-item"><a href="#!">Workflow</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <!-- En-tête du dossier -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-start">
                    <div class="col-md-8">
                        <h5><i class="feather icon-git-branch text-c-blue me-2"></i>Workflow - {{ $dossier->numero_dossier }}</h5>
                        <p class="text-muted m-b-0">{{ $dossier->nom_complet }} • {{ $dossier->type_pension_label }}</p>
                    </div>
                    <div class="col-md-4">
                        <div class="text-end mb-2">
                            <a href="{{ route('pre-liquidations.show', $dossier) }}" class="btn btn-secondary btn-sm">
                                <i class="feather icon-arrow-left me-1"></i>Retour au dossier
                            </a>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-{{ $dossier->etape_color }} me-2">{{ $dossier->etape_label }}</span>
                            <span class="badge bg-{{ $dossier->statut_color }}">{{ $dossier->statut_label }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Workflow visuel -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-git-branch text-c-blue me-2"></i>Étapes du workflow</h5>
            </div>
            <div class="card-block">
                <div class="row">
                    @php
                        $etapes = [
                            'reception' => ['label' => 'Réception', 'icon' => 'file-plus', 'color' => 'info'],
                            'verification' => ['label' => 'Vérification', 'icon' => 'check-square', 'color' => 'warning'],
                            'calcul' => ['label' => 'Calcul', 'icon' => 'calculator', 'color' => 'primary'],
                            'validation' => ['label' => 'Validation', 'icon' => 'user-check', 'color' => 'secondary'],
                            'liquidation' => ['label' => 'Liquidation', 'icon' => 'file-text', 'color' => 'success'],
                            'paiement' => ['label' => 'Paiement', 'icon' => 'credit-card', 'color' => 'success'],
                            'termine' => ['label' => 'Terminé', 'icon' => 'check-circle', 'color' => 'success']
                        ];
                        
                        $etapeActuelleIndex = array_search($dossier->etape_actuelle, array_keys($etapes));
                        $etapeActuelleIndex = $etapeActuelleIndex !== false ? $etapeActuelleIndex : -1;
                    @endphp
                    
                    @foreach($etapes as $etapeKey => $etapeInfo)
                        @php
                            $etapeIndex = array_search($etapeKey, array_keys($etapes));
                            $isCompleted = $etapeIndex < $etapeActuelleIndex || ($etapeKey === $dossier->etape_actuelle && $dossier->statut !== 'nouveau');
                            $isActive = $etapeKey === $dossier->etape_actuelle;
                            $isRejected = $dossier->etape_actuelle === 'rejete';
                        @endphp
                        
                        <div class="col-md-{{ 12 / count($etapes) }} text-center">
                            <div class="workflow-step {{ $isCompleted ? 'completed' : '' }} {{ $isActive ? 'active' : '' }} {{ $isRejected ? 'rejected' : '' }}">
                                <div class="step-icon bg-{{ $isCompleted ? 'success' : ($isActive ? $etapeInfo['color'] : 'light') }}">
                                    <i class="feather icon-{{ $etapeInfo['icon'] }} {{ $isCompleted || $isActive ? 'text-white' : 'text-muted' }}"></i>
                                </div>
                                <h6 class="m-t-10">{{ $etapeInfo['label'] }}</h6>
                                @php
                                    $dateField = 'date_' . $etapeKey;
                                @endphp
                                @if($dossier->$dateField)
                                    <p class="text-success m-b-0">{{ $dossier->$dateField->format('d/m/Y H:i') }}</p>
                                @elseif($isActive)
                                    <p class="text-warning m-b-0">En cours</p>
                                @else
                                    <p class="text-muted m-b-0">En attente</p>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
                
                @if($dossier->etape_actuelle === 'rejete')
                <div class="row mt-4">
                    <div class="col-md-12 text-center">
                        <div class="workflow-step rejected">
                            <div class="step-icon bg-danger">
                                <i class="feather icon-x text-white"></i>
                            </div>
                            <h6 class="m-t-10 text-danger">Dossier Rejeté</h6>
                            <p class="text-danger m-b-0">{{ $dossier->created_at->format('d/m/Y H:i') }}</p>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Détails de l'étape actuelle -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-info text-c-blue me-2"></i>Étape actuelle : {{ $dossier->etape_label }}</h5>
            </div>
            <div class="card-block">
                @switch($dossier->etape_actuelle)
                    @case('reception')
                        <div class="alert alert-info">
                            <h6>Réception du dossier</h6>
                            <p>Le dossier a été reçu et enregistré dans le système. Les documents doivent être vérifiés.</p>
                            <ul>
                                <li>Vérifier la complétude du dossier</li>
                                <li>S'assurer que tous les documents requis sont présents</li>
                                <li>Contrôler l'identité du bénéficiaire</li>
                            </ul>
                        </div>
                        @break
                        
                    @case('verification')
                        <div class="alert alert-warning">
                            <h6>Vérification des documents</h6>
                            <p>Les documents sont en cours de vérification par l'équipe technique.</p>
                            <ul>
                                <li>Contrôle de la validité des pièces justificatives</li>
                                <li>Vérification des périodes de cotisation</li>
                                <li>Validation des informations personnelles</li>
                            </ul>
                        </div>
                        @break
                        
                    @case('calcul')
                        <div class="alert alert-primary">
                            <h6>Calcul de la pension</h6>
                            <p>Le montant de la pension est en cours de calcul selon les règles en vigueur.</p>
                            <ul>
                                <li>Calcul du taux de pension</li>
                                <li>Application des coefficients de majoration</li>
                                <li>Détermination du montant brut et net</li>
                            </ul>
                            @if($dossier->montant_pension_nette)
                                <div class="mt-3">
                                    <strong>Montant calculé :</strong>
                                    <span class="text-success">{{ number_format($dossier->montant_pension_nette, 0, ',', ' ') }} € net</span>
                                </div>
                            @endif
                        </div>
                        @break
                        
                    @case('validation')
                        <div class="alert alert-secondary">
                            <h6>Validation du dossier</h6>
                            <p>Le dossier est en attente de validation par un responsable.</p>
                            <ul>
                                <li>Contrôle final des calculs</li>
                                <li>Validation des montants</li>
                                <li>Approbation pour liquidation</li>
                            </ul>
                        </div>
                        @break
                        
                    @case('liquidation')
                        <div class="alert alert-success">
                            <h6>Liquidation de la pension</h6>
                            <p>Le dossier a été validé et la pension est en cours de liquidation.</p>
                            <ul>
                                <li>Génération des documents de liquidation</li>
                                <li>Préparation des ordres de paiement</li>
                                <li>Notification au bénéficiaire</li>
                            </ul>
                        </div>
                        @break
                        
                    @case('paiement')
                        <div class="alert alert-success">
                            <h6>Mise en paiement</h6>
                            <p>La pension est en cours de mise en paiement.</p>
                            <ul>
                                <li>Traitement des ordres de paiement</li>
                                <li>Virement bancaire en cours</li>
                                <li>Suivi des paiements</li>
                            </ul>
                        </div>
                        @break
                        
                    @case('termine')
                        <div class="alert alert-success">
                            <h6>Dossier terminé</h6>
                            <p>Le dossier a été traité avec succès. La pension est en paiement.</p>
                            <ul>
                                <li>✅ Pension liquidée</li>
                                <li>✅ Premier paiement effectué</li>
                                <li>✅ Bénéficiaire notifié</li>
                            </ul>
                        </div>
                        @break
                        
                    @case('rejete')
                        <div class="alert alert-danger">
                            <h6>Dossier rejeté</h6>
                            <p>Le dossier a été rejeté pour le motif suivant :</p>
                            <div class="bg-light p-3 rounded">
                                <strong>Motif :</strong> {{ $dossier->motif_rejet }}
                            </div>
                        </div>
                        @break
                @endswitch
            </div>
        </div>
    </div>
    
    <!-- Actions disponibles -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-zap text-c-blue me-2"></i>Actions disponibles</h5>
            </div>
            <div class="card-block">
                @if($dossier->etape_actuelle !== 'termine' && $dossier->etape_actuelle !== 'rejete')
                    <button type="button" class="btn btn-success btn-block mb-2" onclick="avancerEtape()">
                        <i class="feather icon-arrow-right me-1"></i>Avancer à l'étape suivante
                    </button>
                @endif
                
                @if($dossier->etape_actuelle === 'calcul' && !$dossier->montant_pension_nette)
                    <button type="button" class="btn btn-primary btn-block mb-2" onclick="calculerPension()">
                        <i class="feather icon-calculator me-1"></i>Calculer la pension
                    </button>
                @endif
                
                @if($dossier->peutEtreValide())
                    <button type="button" class="btn btn-info btn-block mb-2" onclick="validerDossier()">
                        <i class="feather icon-check me-1"></i>Valider le dossier
                    </button>
                @endif
                
                @if($dossier->statut === 'suspendu')
                    <button type="button" class="btn btn-warning btn-block mb-2" onclick="reprendreDossier()">
                        <i class="feather icon-play me-1"></i>Reprendre le dossier
                    </button>
                @else
                    <button type="button" class="btn btn-secondary btn-block mb-2" onclick="suspendreDossier()">
                        <i class="feather icon-pause me-1"></i>Suspendre le dossier
                    </button>
                @endif
                
                @if($dossier->etape_actuelle !== 'rejete')
                    <button type="button" class="btn btn-danger btn-block mb-2" onclick="rejeterDossier()">
                        <i class="feather icon-x me-1"></i>Rejeter le dossier
                    </button>
                @endif
                
                <hr>
                
                <div class="text-center">
                    <small class="text-muted">
                        Dernière mise à jour :<br>
                        {{ $dossier->updated_at->format('d/m/Y à H:i') }}
                    </small>
                </div>
            </div>
        </div>
        
        <!-- Informations utilisateurs -->
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-users text-c-blue me-2"></i>Intervenants</h5>
            </div>
            <div class="card-block">
                @if($dossier->userCreated)
                    <p class="m-b-10">
                        <i class="feather icon-user me-2"></i>
                        <strong>Créé par :</strong><br>
                        <small>{{ $dossier->userCreated->name }}</small>
                    </p>
                @endif
                
                @if($dossier->userAssigned)
                    <p class="m-b-10">
                        <i class="feather icon-user-check me-2"></i>
                        <strong>Assigné à :</strong><br>
                        <small>{{ $dossier->userAssigned->name }}</small>
                    </p>
                @endif
                
                @if($dossier->userValidated)
                    <p class="m-b-10">
                        <i class="feather icon-user-plus me-2"></i>
                        <strong>Validé par :</strong><br>
                        <small>{{ $dossier->userValidated->name }}</small>
                    </p>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* Amélioration de l'en-tête */
.card-header .row {
    margin: 0;
}

.card-header .col-md-4 .text-end {
    margin-bottom: 8px;
}

.card-header .col-md-4 .text-end:last-child {
    margin-bottom: 0;
}

/* Workflow styles */
.workflow-step {
    position: relative;
    margin-bottom: 20px;
}

.step-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 24px;
    border: 3px solid #dee2e6;
}

.workflow-step.completed .step-icon {
    background-color: #28a745 !important;
    border-color: #28a745;
}

.workflow-step.active .step-icon {
    border-color: #007bff;
    animation: pulse 2s infinite;
}

.workflow-step.rejected .step-icon {
    background-color: #dc3545 !important;
    border-color: #dc3545;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
    }
}

.workflow-step::after {
    content: '';
    position: absolute;
    top: 30px;
    left: 50%;
    width: 100%;
    height: 3px;
    background-color: #dee2e6;
    z-index: -1;
}

.workflow-step:last-child::after {
    display: none;
}

.workflow-step.completed::after {
    background-color: #28a745;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-header .col-md-4 .text-end {
        text-align: left !important;
        margin-top: 10px;
    }

    .card-header .row {
        flex-direction: column;
    }
}
</style>
@endpush

@push('scripts')
<script>
// Réutiliser les mêmes fonctions que dans show.blade.php
function avancerEtape() {
    if (confirm('Êtes-vous sûr de vouloir avancer ce dossier à l\'étape suivante ?')) {
        submitAction('{{ route("pre-liquidations.avancer", $dossier) }}', 'PATCH');
    }
}

function calculerPension() {
    if (confirm('Calculer la pension pour ce dossier ?')) {
        submitAction('{{ route("pre-liquidations.calculer", $dossier) }}', 'PATCH');
    }
}

function validerDossier() {
    const observations = prompt('Observations (optionnel) :');
    if (observations !== null) {
        submitAction('{{ route("pre-liquidations.valider", $dossier) }}', 'PATCH', {observations: observations});
    }
}

function rejeterDossier() {
    const motif = prompt('Motif de rejet (obligatoire) :');
    if (motif && motif.trim()) {
        submitAction('{{ route("pre-liquidations.rejeter", $dossier) }}', 'PATCH', {motif_rejet: motif});
    } else if (motif !== null) {
        alert('Le motif de rejet est obligatoire.');
    }
}

function suspendreDossier() {
    const motif = prompt('Motif de suspension (obligatoire) :');
    if (motif && motif.trim()) {
        submitAction('{{ route("pre-liquidations.suspendre", $dossier) }}', 'PATCH', {motif_suspension: motif});
    } else if (motif !== null) {
        alert('Le motif de suspension est obligatoire.');
    }
}

function reprendreDossier() {
    if (confirm('Reprendre ce dossier suspendu ?')) {
        submitAction('{{ route("pre-liquidations.reprendre", $dossier) }}', 'PATCH');
    }
}

function submitAction(url, method, data = {}) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = url;
    
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '{{ csrf_token() }}';
    form.appendChild(csrfToken);
    
    const methodField = document.createElement('input');
    methodField.type = 'hidden';
    methodField.name = '_method';
    methodField.value = method;
    form.appendChild(methodField);
    
    // Ajouter les données supplémentaires
    for (const [key, value] of Object.entries(data)) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        form.appendChild(input);
    }
    
    document.body.appendChild(form);
    form.submit();
}
</script>
@endpush
