@extends('layouts.template')

@section('title', 'Dossier ' . $dossier->numero_dossier . ' - CRFM')

@section('page-header')
@section('page-title', 'Dossier de Pré-liquidation')
@section('page-description', 'Détails du dossier ' . $dossier->numero_dossier)
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Dossiers</a></li>
        <li class="breadcrumb-item"><a href="{{ route('pre-liquidations.index') }}">Pré-liquidations</a></li>
        <li class="breadcrumb-item"><a href="#!">{{ $dossier->numero_dossier }}</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <!-- En-tête du dossier -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5><i class="feather icon-folder text-c-blue me-2"></i>{{ $dossier->numero_dossier }}</h5>
                        <p class="text-muted m-b-0">{{ $dossier->nom_complet }} • {{ $dossier->type_pension_label }}</p>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="btn-group mb-2">
                            <a href="{{ route('pre-liquidations.index') }}" class="btn btn-secondary btn-sm">
                                <i class="feather icon-arrow-left me-1"></i>Retour
                            </a>
                            @if($dossier->peutEtreModifie())
                                <a href="{{ route('pre-liquidations.edit', $dossier) }}" class="btn btn-warning btn-sm">
                                    <i class="feather icon-edit me-1"></i>Modifier
                                </a>
                            @endif
                            <a href="{{ route('pre-liquidations.workflow', $dossier) }}" class="btn btn-info btn-sm">
                                <i class="feather icon-git-branch me-1"></i>Workflow
                            </a>
                        </div>
                        <div>
                            <span class="badge bg-{{ $dossier->etape_color }} me-2">{{ $dossier->etape_label }}</span>
                            <span class="badge bg-{{ $dossier->statut_color }} me-2">{{ $dossier->statut_label }}</span>
                            <span class="badge bg-{{ $dossier->priorite_color }}">{{ $dossier->priorite_label }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Informations du bénéficiaire -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-user text-c-blue me-2"></i>Bénéficiaire</h5>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-sm-6">
                        <p class="m-b-10 f-w-600">Nom complet</p>
                        <h6 class="text-muted f-w-400">{{ $dossier->nom_complet }}</h6>
                    </div>
                    <div class="col-sm-6">
                        <p class="m-b-10 f-w-600">Sexe</p>
                        <h6 class="text-muted f-w-400">{{ $dossier->sexe === 'M' ? 'Masculin' : 'Féminin' }}</h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <p class="m-b-10 f-w-600">Date de naissance</p>
                        <h6 class="text-muted f-w-400">{{ $dossier->date_naissance->format('d/m/Y') }} ({{ $dossier->age }} ans)</h6>
                    </div>
                    <div class="col-sm-6">
                        <p class="m-b-10 f-w-600">Lieu de naissance</p>
                        <h6 class="text-muted f-w-400">{{ $dossier->lieu_naissance ?: 'Non renseigné' }}</h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <p class="m-b-10 f-w-600">N° Sécurité Sociale</p>
                        <h6 class="text-muted f-w-400">{{ $dossier->numero_securite_sociale ?: 'Non renseigné' }}</h6>
                    </div>
                    <div class="col-sm-6">
                        <p class="m-b-10 f-w-600">N° Adhérent</p>
                        <h6 class="text-muted f-w-400">{{ $dossier->numero_adherent ?: 'Non renseigné' }}</h6>
                    </div>
                </div>
                @if($dossier->adresse || $dossier->telephone || $dossier->email)
                <hr>
                <h6 class="text-muted">Contact</h6>
                @if($dossier->adresse)
                    <p class="m-b-5"><i class="feather icon-map-pin me-2"></i>{{ $dossier->adresse }}</p>
                @endif
                @if($dossier->telephone)
                    <p class="m-b-5"><i class="feather icon-phone me-2"></i>{{ $dossier->telephone }}</p>
                @endif
                @if($dossier->email)
                    <p class="m-b-5"><i class="feather icon-mail me-2"></i>{{ $dossier->email }}</p>
                @endif
                @endif
            </div>
        </div>
    </div>

    <!-- Informations de la pension -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-award text-c-blue me-2"></i>Pension</h5>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-sm-6">
                        <p class="m-b-10 f-w-600">Type de pension</p>
                        <h6 class="text-muted f-w-400">{{ $dossier->type_pension_label }}</h6>
                    </div>
                    <div class="col-sm-6">
                        <p class="m-b-10 f-w-600">Date de départ</p>
                        <h6 class="text-muted f-w-400">{{ $dossier->date_depart_retraite->format('d/m/Y') }}</h6>
                    </div>
                </div>
                @if($dossier->date_fin_activite)
                <div class="row">
                    <div class="col-sm-6">
                        <p class="m-b-10 f-w-600">Date fin d'activité</p>
                        <h6 class="text-muted f-w-400">{{ $dossier->date_fin_activite->format('d/m/Y') }}</h6>
                    </div>
                </div>
                @endif
                
                @if($dossier->montant_pension_nette)
                <hr>
                <div class="row">
                    <div class="col-sm-6">
                        <p class="m-b-10 f-w-600">Montant brut</p>
                        <h6 class="text-success f-w-600">{{ number_format($dossier->montant_pension_brute, 0, ',', ' ') }} €</h6>
                    </div>
                    <div class="col-sm-6">
                        <p class="m-b-10 f-w-600">Montant net</p>
                        <h6 class="text-success f-w-600">{{ number_format($dossier->montant_pension_nette, 0, ',', ' ') }} €</h6>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <p class="m-b-10 f-w-600">Taux de pension</p>
                        <h6 class="text-muted f-w-400">{{ $dossier->taux_pension }}%</h6>
                    </div>
                    <div class="col-sm-6">
                        <p class="m-b-10 f-w-600">Coefficient</p>
                        <h6 class="text-muted f-w-400">{{ $dossier->coefficient_majoration }}</h6>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Informations professionnelles -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-briefcase text-c-blue me-2"></i>Carrière professionnelle</h5>
            </div>
            <div class="card-block">
                @if($dossier->dernier_employeur || $dossier->fonction)
                <div class="row">
                    @if($dossier->dernier_employeur)
                    <div class="col-sm-6">
                        <p class="m-b-10 f-w-600">Dernier employeur</p>
                        <h6 class="text-muted f-w-400">{{ $dossier->dernier_employeur }}</h6>
                    </div>
                    @endif
                    @if($dossier->fonction)
                    <div class="col-sm-6">
                        <p class="m-b-10 f-w-600">Fonction</p>
                        <h6 class="text-muted f-w-400">{{ $dossier->fonction }}</h6>
                    </div>
                    @endif
                </div>
                @endif
                
                <div class="row">
                    <div class="col-sm-6">
                        <p class="m-b-10 f-w-600">Durée de cotisation</p>
                        <h6 class="text-muted f-w-400">{{ $dossier->duree_cotisation_annees }} ans ({{ $dossier->duree_cotisation_mois }} mois)</h6>
                    </div>
                    @if($dossier->salaire_reference)
                    <div class="col-sm-6">
                        <p class="m-b-10 f-w-600">Salaire de référence</p>
                        <h6 class="text-muted f-w-400">{{ number_format($dossier->salaire_reference, 0, ',', ' ') }} €</h6>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Workflow et statut -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-git-branch text-c-blue me-2"></i>Workflow</h5>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-sm-6">
                        <p class="m-b-10 f-w-600">Étape actuelle</p>
                        <span class="badge bg-{{ $dossier->etape_color }}">{{ $dossier->etape_label }}</span>
                    </div>
                    <div class="col-sm-6">
                        <p class="m-b-10 f-w-600">Statut</p>
                        <span class="badge bg-{{ $dossier->statut_color }}">{{ $dossier->statut_label }}</span>
                    </div>
                </div>
                
                <hr>
                
                <!-- Dates du workflow -->
                @if($dossier->date_reception)
                    <p class="m-b-5"><i class="feather icon-check-circle text-success me-2"></i>Réception : {{ $dossier->date_reception->format('d/m/Y H:i') }}</p>
                @endif
                @if($dossier->date_verification)
                    <p class="m-b-5"><i class="feather icon-check-circle text-success me-2"></i>Vérification : {{ $dossier->date_verification->format('d/m/Y H:i') }}</p>
                @endif
                @if($dossier->date_calcul)
                    <p class="m-b-5"><i class="feather icon-check-circle text-success me-2"></i>Calcul : {{ $dossier->date_calcul->format('d/m/Y H:i') }}</p>
                @endif
                @if($dossier->date_validation)
                    <p class="m-b-5"><i class="feather icon-check-circle text-success me-2"></i>Validation : {{ $dossier->date_validation->format('d/m/Y H:i') }}</p>
                @endif
                @if($dossier->date_liquidation)
                    <p class="m-b-5"><i class="feather icon-check-circle text-success me-2"></i>Liquidation : {{ $dossier->date_liquidation->format('d/m/Y H:i') }}</p>
                @endif
                @if($dossier->date_paiement)
                    <p class="m-b-5"><i class="feather icon-check-circle text-success me-2"></i>Paiement : {{ $dossier->date_paiement->format('d/m/Y H:i') }}</p>
                @endif

                <hr>

                <!-- Utilisateurs -->
                @if($dossier->userCreated)
                    <p class="m-b-5"><i class="feather icon-user me-2"></i>Créé par : {{ $dossier->userCreated->name }}</p>
                @endif
                @if($dossier->userAssigned)
                    <p class="m-b-5"><i class="feather icon-user-check me-2"></i>Assigné à : {{ $dossier->userAssigned->name }}</p>
                @endif
                @if($dossier->userValidated)
                    <p class="m-b-5"><i class="feather icon-user-plus me-2"></i>Validé par : {{ $dossier->userValidated->name }}</p>
                @endif
            </div>
        </div>
    </div>
</div>

@if($dossier->observations || $dossier->motif_rejet)
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-message-square text-c-blue me-2"></i>Observations</h5>
            </div>
            <div class="card-block">
                @if($dossier->observations)
                    <div class="alert alert-info">
                        <h6>Observations :</h6>
                        <p class="m-b-0">{{ $dossier->observations }}</p>
                    </div>
                @endif
                @if($dossier->motif_rejet)
                    <div class="alert alert-danger">
                        <h6>Motif de rejet :</h6>
                        <p class="m-b-0">{{ $dossier->motif_rejet }}</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endif

<!-- Actions rapides -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-zap text-c-blue me-2"></i>Actions</h5>
            </div>
            <div class="card-block">
                <div class="btn-group me-2">
                    @if($dossier->etape_actuelle !== 'termine' && $dossier->etape_actuelle !== 'rejete')
                        <button type="button" class="btn btn-success" onclick="avancerEtape()">
                            <i class="feather icon-arrow-right me-1"></i>Avancer étape
                        </button>
                    @endif
                    
                    @if($dossier->etape_actuelle === 'calcul' && !$dossier->montant_pension_nette)
                        <button type="button" class="btn btn-primary" onclick="calculerPension()">
                            <i class="feather icon-calculator me-1"></i>Calculer pension
                        </button>
                    @endif
                    
                    @if($dossier->peutEtreValide())
                        <button type="button" class="btn btn-info" onclick="validerDossier()">
                            <i class="feather icon-check me-1"></i>Valider
                        </button>
                    @endif
                </div>
                
                <div class="btn-group">
                    @if($dossier->statut === 'suspendu')
                        <button type="button" class="btn btn-warning" onclick="reprendreDossier()">
                            <i class="feather icon-play me-1"></i>Reprendre
                        </button>
                    @else
                        <button type="button" class="btn btn-secondary" onclick="suspendreDossier()">
                            <i class="feather icon-pause me-1"></i>Suspendre
                        </button>
                    @endif
                    
                    @if($dossier->etape_actuelle !== 'rejete')
                        <button type="button" class="btn btn-danger" onclick="rejeterDossier()">
                            <i class="feather icon-x me-1"></i>Rejeter
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function avancerEtape() {
    if (confirm('Êtes-vous sûr de vouloir avancer ce dossier à l\'étape suivante ?')) {
        submitAction('{{ route("pre-liquidations.avancer", $dossier) }}', 'PATCH');
    }
}

function calculerPension() {
    if (confirm('Calculer la pension pour ce dossier ?')) {
        submitAction('{{ route("pre-liquidations.calculer", $dossier) }}', 'PATCH');
    }
}

function validerDossier() {
    const observations = prompt('Observations (optionnel) :');
    if (observations !== null) {
        submitAction('{{ route("pre-liquidations.valider", $dossier) }}', 'PATCH', {observations: observations});
    }
}

function rejeterDossier() {
    const motif = prompt('Motif de rejet (obligatoire) :');
    if (motif && motif.trim()) {
        submitAction('{{ route("pre-liquidations.rejeter", $dossier) }}', 'PATCH', {motif_rejet: motif});
    } else if (motif !== null) {
        alert('Le motif de rejet est obligatoire.');
    }
}

function suspendreDossier() {
    const motif = prompt('Motif de suspension (obligatoire) :');
    if (motif && motif.trim()) {
        submitAction('{{ route("pre-liquidations.suspendre", $dossier) }}', 'PATCH', {motif_suspension: motif});
    } else if (motif !== null) {
        alert('Le motif de suspension est obligatoire.');
    }
}

function reprendreDossier() {
    if (confirm('Reprendre ce dossier suspendu ?')) {
        submitAction('{{ route("pre-liquidations.reprendre", $dossier) }}', 'PATCH');
    }
}

function submitAction(url, method, data = {}) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = url;
    
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '{{ csrf_token() }}';
    form.appendChild(csrfToken);
    
    const methodField = document.createElement('input');
    methodField.type = 'hidden';
    methodField.name = '_method';
    methodField.value = method;
    form.appendChild(methodField);
    
    // Ajouter les données supplémentaires
    for (const [key, value] of Object.entries(data)) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        form.appendChild(input);
    }
    
    document.body.appendChild(form);
    form.submit();
}
</script>
@endpush
