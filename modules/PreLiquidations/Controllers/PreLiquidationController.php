<?php

namespace Modules\PreLiquidations\Controllers;

use App\Http\Controllers\BaseController;
use Modules\PreLiquidations\Models\PreLiquidation;
use Modules\PreLiquidations\Services\PreLiquidationService;
use Modules\PreLiquidations\Repositories\PreLiquidationRepository;
use Modules\PreLiquidations\Requests\CreatePreLiquidationRequest;
use Modules\PreLiquidations\Requests\UpdatePreLiquidationRequest;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Exception;

class PreLiquidationController extends BaseController
{
    protected PreLiquidationService $preLiquidationService;
    protected PreLiquidationRepository $preLiquidationRepository;

    public function __construct(
        PreLiquidationService $preLiquidationService,
        PreLiquidationRepository $preLiquidationRepository
    ) {
        $this->preLiquidationService = $preLiquidationService;
        $this->preLiquidationRepository = $preLiquidationRepository;
    }

    /**
     * Afficher la liste des dossiers
     */
    public function index(Request $request): View
    {
        $filters = $request->only([
            'etape', 'statut', 'type_pension', 'priorite', 'user_assigned',
            'date_depart_debut', 'date_depart_fin', 'search', 'urgents', 'en_retard'
        ]);
        $perPage = $request->get('per_page', 15);

        $dossiers = $this->preLiquidationRepository->getAllWithFilters($filters, $perPage);
        $statistics = $this->preLiquidationService->getStatistics();
        $urgents = $this->preLiquidationRepository->getUrgents();
        $enRetard = $this->preLiquidationRepository->getEnRetard();

        return view('pre-liquidations::index', compact(
            'dossiers',
            'statistics',
            'urgents',
            'enRetard',
            'filters'
        ));
    }

    /**
     * Afficher le formulaire de création
     */
    public function create(): View
    {
        return view('pre-liquidations::create');
    }

    /**
     * Enregistrer un nouveau dossier
     */
    public function store(CreatePreLiquidationRequest $request): RedirectResponse
    {
        try {
            $dossier = $this->preLiquidationService->createDossier(
                $request->validated(),
                auth()->user()
            );

            return $this->successRedirect(
                'pre-liquidations.show',
                'Dossier de pré-liquidation créé avec succès !',
                ['pre_liquidation' => $dossier]
            );

        } catch (Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Erreur lors de la création : ' . $e->getMessage());
        }
    }

    /**
     * Afficher un dossier
     */
    public function show(PreLiquidation $preLiquidation): View
    {
        $dossier = $this->preLiquidationRepository->findWithRelations($preLiquidation->id);
        
        if (!$dossier) {
            abort(404, 'Dossier non trouvé');
        }

        return view('pre-liquidations::show', compact('dossier'));
    }

    /**
     * Afficher le formulaire de modification
     */
    public function edit(PreLiquidation $preLiquidation): View
    {
        if (!$preLiquidation->peutEtreModifie()) {
            return back()->with('error', 'Ce dossier ne peut plus être modifié.');
        }

        return view('pre-liquidations::edit', compact('preLiquidation'));
    }

    /**
     * Mettre à jour un dossier
     */
    public function update(UpdatePreLiquidationRequest $request, PreLiquidation $preLiquidation): RedirectResponse
    {
        try {
            $dossier = $this->preLiquidationService->updateDossier(
                $preLiquidation,
                $request->validated(),
                auth()->user()
            );

            return $this->successRedirect(
                'pre-liquidations.show',
                'Dossier mis à jour avec succès !',
                ['pre_liquidation' => $dossier]
            );

        } catch (Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Erreur lors de la mise à jour : ' . $e->getMessage());
        }
    }

    /**
     * Supprimer un dossier
     */
    public function destroy(PreLiquidation $preLiquidation): RedirectResponse
    {
        try {
            $this->preLiquidationService->deleteDossier($preLiquidation, auth()->user());

            return $this->successRedirect(
                'pre-liquidations.index',
                'Dossier supprimé avec succès !'
            );

        } catch (Exception $e) {
            return back()->with('error', 'Erreur lors de la suppression : ' . $e->getMessage());
        }
    }

    /**
     * Avancer un dossier à l'étape suivante
     */
    public function avancerEtape(PreLiquidation $preLiquidation): RedirectResponse
    {
        try {
            $this->preLiquidationService->avancerEtape($preLiquidation, auth()->user());

            return back()->with('success', 'Dossier avancé à l\'étape suivante avec succès !');

        } catch (Exception $e) {
            return back()->with('error', 'Erreur : ' . $e->getMessage());
        }
    }

    /**
     * Calculer la pension
     */
    public function calculerPension(PreLiquidation $preLiquidation): RedirectResponse
    {
        try {
            $this->preLiquidationService->calculerPension($preLiquidation, auth()->user());

            return back()->with('success', 'Pension calculée avec succès !');

        } catch (Exception $e) {
            return back()->with('error', 'Erreur lors du calcul : ' . $e->getMessage());
        }
    }

    /**
     * Valider un dossier
     */
    public function valider(Request $request, PreLiquidation $preLiquidation): RedirectResponse
    {
        $request->validate([
            'observations' => 'nullable|string|max:1000'
        ]);

        try {
            $this->preLiquidationService->validerDossier(
                $preLiquidation,
                auth()->user(),
                $request->get('observations')
            );

            return back()->with('success', 'Dossier validé avec succès !');

        } catch (Exception $e) {
            return back()->with('error', 'Erreur : ' . $e->getMessage());
        }
    }

    /**
     * Rejeter un dossier
     */
    public function rejeter(Request $request, PreLiquidation $preLiquidation): RedirectResponse
    {
        $request->validate([
            'motif_rejet' => 'required|string|max:1000'
        ]);

        try {
            $this->preLiquidationService->rejeterDossier(
                $preLiquidation,
                auth()->user(),
                $request->get('motif_rejet')
            );

            return back()->with('success', 'Dossier rejeté avec succès !');

        } catch (Exception $e) {
            return back()->with('error', 'Erreur : ' . $e->getMessage());
        }
    }

    /**
     * Suspendre un dossier
     */
    public function suspendre(Request $request, PreLiquidation $preLiquidation): RedirectResponse
    {
        $request->validate([
            'motif_suspension' => 'required|string|max:1000'
        ]);

        try {
            $this->preLiquidationService->suspendreDossier(
                $preLiquidation,
                auth()->user(),
                $request->get('motif_suspension')
            );

            return back()->with('success', 'Dossier suspendu avec succès !');

        } catch (Exception $e) {
            return back()->with('error', 'Erreur : ' . $e->getMessage());
        }
    }

    /**
     * Reprendre un dossier suspendu
     */
    public function reprendre(PreLiquidation $preLiquidation): RedirectResponse
    {
        try {
            $this->preLiquidationService->reprendreDossier($preLiquidation, auth()->user());

            return back()->with('success', 'Dossier repris avec succès !');

        } catch (Exception $e) {
            return back()->with('error', 'Erreur : ' . $e->getMessage());
        }
    }

    /**
     * API - Statistiques pour le dashboard
     */
    public function apiStatistics(): JsonResponse
    {
        try {
            $statistics = $this->preLiquidationService->getStatistics();
            
            return response()->json([
                'success' => true,
                'data' => $statistics
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des statistiques'
            ], 500);
        }
    }

    /**
     * API - Recherche de dossiers
     */
    public function apiSearch(Request $request): JsonResponse
    {
        $request->validate([
            'term' => 'required|string|min:2'
        ]);

        try {
            $dossiers = $this->preLiquidationRepository->search($request->get('term'));
            
            return response()->json([
                'success' => true,
                'data' => $dossiers->map(function ($dossier) {
                    return [
                        'id' => $dossier->id,
                        'numero_dossier' => $dossier->numero_dossier,
                        'nom_complet' => $dossier->nom_complet,
                        'type_pension' => $dossier->type_pension_label,
                        'etape' => $dossier->etape_label,
                        'statut' => $dossier->statut_label
                    ];
                })
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la recherche'
            ], 500);
        }
    }

    /**
     * Workflow - Vue du workflow
     */
    public function workflow(PreLiquidation $preLiquidation): View
    {
        $dossier = $this->preLiquidationRepository->findWithRelations($preLiquidation->id);
        
        return view('pre-liquidations::workflow', compact('dossier'));
    }
}
