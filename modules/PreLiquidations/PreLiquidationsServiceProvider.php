<?php

namespace Modules\PreLiquidations;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;

class PreLiquidationsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Enregistrer les services
        $this->app->bind(
            \Modules\PreLiquidations\Repositories\PreLiquidationRepository::class,
            \Modules\PreLiquidations\Repositories\PreLiquidationRepository::class
        );

        $this->app->bind(
            \Modules\PreLiquidations\Services\PreLiquidationService::class,
            \Modules\PreLiquidations\Services\PreLiquidationService::class
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Charger les routes
        $this->loadRoutesFrom(__DIR__ . '/routes.php');

        // Charger les vues
        $this->loadViewsFrom(__DIR__ . '/Views', 'pre-liquidations');

        // Publier les assets si nécessaire
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__ . '/Views' => resource_path('views/vendor/pre-liquidations'),
            ], 'pre-liquidations-views');
        }
    }
}
