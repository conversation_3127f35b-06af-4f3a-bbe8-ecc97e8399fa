<?php

use Illuminate\Support\Facades\Route;
use Modules\PreLiquidations\Controllers\PreLiquidationController;

/*
|--------------------------------------------------------------------------
| Pre-Liquidations Module Routes
|--------------------------------------------------------------------------
*/

Route::middleware(['web', 'auth'])->group(function () {

    // Routes spécifiques (DOIVENT être avant les routes avec paramètres)
    Route::get('/pre-liquidations/create', [PreLiquidationController::class, 'create'])->name('pre-liquidations.create');

    // Routes de base CRUD
    Route::get('/pre-liquidations', [PreLiquidationController::class, 'index'])->name('pre-liquidations.index');
    Route::post('/pre-liquidations', [PreLiquidationController::class, 'store'])->name('pre-liquidations.store');
    Route::get('/pre-liquidations/{pre_liquidation}', [PreLiquidationController::class, 'show'])->name('pre-liquidations.show');
    Route::get('/pre-liquidations/{pre_liquidation}/edit', [PreLiquidationController::class, 'edit'])->name('pre-liquidations.edit');
    Route::put('/pre-liquidations/{pre_liquidation}', [PreLiquidationController::class, 'update'])->name('pre-liquidations.update');
    Route::delete('/pre-liquidations/{pre_liquidation}', [PreLiquidationController::class, 'destroy'])->name('pre-liquidations.destroy');

    // Actions spécifiques sur les dossiers
    Route::patch('/pre-liquidations/{pre_liquidation}/avancer', [PreLiquidationController::class, 'avancerEtape'])->name('pre-liquidations.avancer');
    Route::patch('/pre-liquidations/{pre_liquidation}/calculer', [PreLiquidationController::class, 'calculerPension'])->name('pre-liquidations.calculer');
    Route::patch('/pre-liquidations/{pre_liquidation}/valider', [PreLiquidationController::class, 'valider'])->name('pre-liquidations.valider');
    Route::patch('/pre-liquidations/{pre_liquidation}/rejeter', [PreLiquidationController::class, 'rejeter'])->name('pre-liquidations.rejeter');
    Route::patch('/pre-liquidations/{pre_liquidation}/suspendre', [PreLiquidationController::class, 'suspendre'])->name('pre-liquidations.suspendre');
    Route::patch('/pre-liquidations/{pre_liquidation}/reprendre', [PreLiquidationController::class, 'reprendre'])->name('pre-liquidations.reprendre');

    // Vue workflow
    Route::get('/pre-liquidations/{pre_liquidation}/workflow', [PreLiquidationController::class, 'workflow'])->name('pre-liquidations.workflow');

    // API Routes pour AJAX
    Route::prefix('api/pre-liquidations')->name('api.pre-liquidations.')->group(function () {
        Route::get('/statistics', [PreLiquidationController::class, 'apiStatistics'])->name('statistics');
        Route::get('/search', [PreLiquidationController::class, 'apiSearch'])->name('search');
    });

});
