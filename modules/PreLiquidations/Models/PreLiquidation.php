<?php

namespace Modules\PreLiquidations\Models;

use App\Models\BaseModel;
use App\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class PreLiquidation extends BaseModel
{
    use SoftDeletes;

    protected $fillable = [
        'uuid',
        'numero_dossier',
        'date_creation',
        'date_depot',
        'nom_beneficiaire',
        'prenom_beneficiaire',
        'date_naissance',
        'lieu_naissance',
        'sexe',
        'numero_securite_sociale',
        'numero_adherent',
        'adresse',
        'telephone',
        'email',
        'type_pension',
        'date_depart_retraite',
        'date_fin_activite',
        'dernier_employeur',
        'fonction',
        'duree_cotisation_mois',
        'salaire_reference',
        'montant_pension_brute',
        'montant_pension_nette',
        'taux_pension',
        'coefficient_majoration',
        'etape_actuelle',
        'statut',
        'date_reception',
        'date_verification',
        'date_calcul',
        'date_validation',
        'date_liquidation',
        'date_paiement',
        'user_created_id',
        'user_assigned_id',
        'user_validated_id',
        'documents_requis',
        'documents_fournis',
        'observations',
        'motif_rejet',
        'priorite',
        'dossier_complet'
    ];

    protected $casts = [
        'date_creation' => 'date',
        'date_depot' => 'date',
        'date_naissance' => 'date',
        'date_depart_retraite' => 'date',
        'date_fin_activite' => 'date',
        'salaire_reference' => 'decimal:2',
        'montant_pension_brute' => 'decimal:2',
        'montant_pension_nette' => 'decimal:2',
        'taux_pension' => 'decimal:2',
        'coefficient_majoration' => 'decimal:4',
        'date_reception' => 'datetime',
        'date_verification' => 'datetime',
        'date_calcul' => 'datetime',
        'date_validation' => 'datetime',
        'date_liquidation' => 'datetime',
        'date_paiement' => 'datetime',
        'documents_requis' => 'array',
        'documents_fournis' => 'array',
        'dossier_complet' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    // Constantes pour les étapes
    const ETAPE_RECEPTION = 'reception';
    const ETAPE_VERIFICATION = 'verification';
    const ETAPE_CALCUL = 'calcul';
    const ETAPE_VALIDATION = 'validation';
    const ETAPE_LIQUIDATION = 'liquidation';
    const ETAPE_PAIEMENT = 'paiement';
    const ETAPE_TERMINE = 'termine';
    const ETAPE_REJETE = 'rejete';

    const ETAPES = [
        self::ETAPE_RECEPTION => 'Réception',
        self::ETAPE_VERIFICATION => 'Vérification',
        self::ETAPE_CALCUL => 'Calcul',
        self::ETAPE_VALIDATION => 'Validation',
        self::ETAPE_LIQUIDATION => 'Liquidation',
        self::ETAPE_PAIEMENT => 'Paiement',
        self::ETAPE_TERMINE => 'Terminé',
        self::ETAPE_REJETE => 'Rejeté',
    ];

    // Constantes pour les statuts
    const STATUT_NOUVEAU = 'nouveau';
    const STATUT_EN_COURS = 'en_cours';
    const STATUT_PRET = 'pret';
    const STATUT_LIQUIDE = 'liquide';
    const STATUT_PAYE = 'paye';
    const STATUT_SUSPENDU = 'suspendu';
    const STATUT_REJETE = 'rejete';
    const STATUT_ARCHIVE = 'archive';

    const STATUTS = [
        self::STATUT_NOUVEAU => 'Nouveau',
        self::STATUT_EN_COURS => 'En cours',
        self::STATUT_PRET => 'Prêt',
        self::STATUT_LIQUIDE => 'Liquidé',
        self::STATUT_PAYE => 'Payé',
        self::STATUT_SUSPENDU => 'Suspendu',
        self::STATUT_REJETE => 'Rejeté',
        self::STATUT_ARCHIVE => 'Archivé',
    ];

    // Constantes pour les types de pension
    const TYPE_VIEILLESSE = 'vieillesse';
    const TYPE_ANTICIPEE = 'anticipee';
    const TYPE_INVALIDITE = 'invalidite';
    const TYPE_SURVIVANT = 'survivant';
    const TYPE_ORPHELIN = 'orphelin';

    const TYPES_PENSION = [
        self::TYPE_VIEILLESSE => 'Pension de vieillesse',
        self::TYPE_ANTICIPEE => 'Pension anticipée',
        self::TYPE_INVALIDITE => 'Pension d\'invalidité',
        self::TYPE_SURVIVANT => 'Pension de survivant',
        self::TYPE_ORPHELIN => 'Pension d\'orphelin',
    ];

    // Constantes pour les priorités
    const PRIORITE_NORMALE = 'normale';
    const PRIORITE_HAUTE = 'haute';
    const PRIORITE_URGENTE = 'urgente';

    const PRIORITES = [
        self::PRIORITE_NORMALE => 'Normale',
        self::PRIORITE_HAUTE => 'Haute',
        self::PRIORITE_URGENTE => 'Urgente',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($preLiquidation) {
            if (empty($preLiquidation->uuid)) {
                $preLiquidation->uuid = Str::uuid();
            }
            if (empty($preLiquidation->numero_dossier)) {
                $preLiquidation->numero_dossier = self::generateNumeroDossier();
            }
            if (empty($preLiquidation->date_creation)) {
                $preLiquidation->date_creation = now()->toDateString();
            }
        });
    }

    /**
     * Relations
     */
    public function userCreated(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_created_id');
    }

    public function userAssigned(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_assigned_id');
    }

    public function userValidated(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_validated_id');
    }

    /**
     * Accessors
     */
    public function getNomCompletAttribute(): string
    {
        return $this->prenom_beneficiaire . ' ' . $this->nom_beneficiaire;
    }

    public function getEtapeLabelAttribute(): string
    {
        return self::ETAPES[$this->etape_actuelle] ?? 'Inconnu';
    }

    public function getStatutLabelAttribute(): string
    {
        return self::STATUTS[$this->statut] ?? 'Inconnu';
    }

    public function getTypePensionLabelAttribute(): string
    {
        return self::TYPES_PENSION[$this->type_pension] ?? 'Inconnu';
    }

    public function getPrioriteLabelAttribute(): string
    {
        return self::PRIORITES[$this->priorite] ?? 'Normale';
    }

    public function getEtapeColorAttribute(): string
    {
        return match($this->etape_actuelle) {
            self::ETAPE_RECEPTION => 'info',
            self::ETAPE_VERIFICATION => 'warning',
            self::ETAPE_CALCUL => 'primary',
            self::ETAPE_VALIDATION => 'secondary',
            self::ETAPE_LIQUIDATION => 'success',
            self::ETAPE_PAIEMENT => 'success',
            self::ETAPE_TERMINE => 'success',
            self::ETAPE_REJETE => 'danger',
            default => 'secondary'
        };
    }

    public function getStatutColorAttribute(): string
    {
        return match($this->statut) {
            self::STATUT_NOUVEAU => 'info',
            self::STATUT_EN_COURS => 'warning',
            self::STATUT_PRET => 'primary',
            self::STATUT_LIQUIDE => 'success',
            self::STATUT_PAYE => 'success',
            self::STATUT_SUSPENDU => 'secondary',
            self::STATUT_REJETE => 'danger',
            self::STATUT_ARCHIVE => 'dark',
            default => 'secondary'
        };
    }

    public function getPrioriteColorAttribute(): string
    {
        return match($this->priorite) {
            self::PRIORITE_NORMALE => 'secondary',
            self::PRIORITE_HAUTE => 'warning',
            self::PRIORITE_URGENTE => 'danger',
            default => 'secondary'
        };
    }

    public function getAgeAttribute(): int
    {
        return $this->date_naissance ? $this->date_naissance->diffInYears(now()) : 0;
    }

    public function getDureeCotisationAnneesAttribute(): float
    {
        return round($this->duree_cotisation_mois / 12, 1);
    }

    public function getMontantPensionFormateAttribute(): string
    {
        return $this->montant_pension_nette ? 
            number_format($this->montant_pension_nette, 0, ',', ' ') . ' €' : 
            'Non calculé';
    }

    /**
     * Scopes
     */
    public function scopeParEtape($query, $etape)
    {
        return $query->where('etape_actuelle', $etape);
    }

    public function scopeParStatut($query, $statut)
    {
        return $query->where('statut', $statut);
    }

    public function scopeParTypePension($query, $type)
    {
        return $query->where('type_pension', $type);
    }

    public function scopeParPriorite($query, $priorite)
    {
        return $query->where('priorite', $priorite);
    }

    public function scopeAssigneA($query, $userId)
    {
        return $query->where('user_assigned_id', $userId);
    }

    public function scopeEnRetard($query)
    {
        return $query->where('date_depart_retraite', '<', now()->toDateString())
                    ->whereNotIn('etape_actuelle', [self::ETAPE_TERMINE, self::ETAPE_REJETE]);
    }

    public function scopeUrgents($query)
    {
        return $query->where('priorite', self::PRIORITE_URGENTE)
                    ->orWhere(function($q) {
                        $q->where('date_depart_retraite', '<=', now()->addDays(30))
                          ->whereNotIn('etape_actuelle', [self::ETAPE_TERMINE, self::ETAPE_REJETE]);
                    });
    }

    /**
     * Méthodes métier
     */
    public function peutEtreModifie(): bool
    {
        return in_array($this->etape_actuelle, [
            self::ETAPE_RECEPTION,
            self::ETAPE_VERIFICATION
        ]) && $this->statut !== self::STATUT_REJETE;
    }

    public function peutEtreValide(): bool
    {
        return $this->etape_actuelle === self::ETAPE_CALCUL &&
               $this->statut === self::STATUT_PRET;
    }

    public function peutEtreLiquide(): bool
    {
        return $this->etape_actuelle === self::ETAPE_VALIDATION &&
               $this->statut === self::STATUT_PRET;
    }

    public function peutEtrePaye(): bool
    {
        return $this->etape_actuelle === self::ETAPE_LIQUIDATION &&
               $this->statut === self::STATUT_LIQUIDE;
    }

    public function avancerEtape(User $user): void
    {
        $etapeSuivante = $this->getEtapeSuivante();
        if ($etapeSuivante) {
            $this->etape_actuelle = $etapeSuivante;
            $this->marquerDateEtape($etapeSuivante);
            $this->user_assigned_id = $user->id;
            $this->save();
        }
    }

    public function rejeter(User $user, string $motif): void
    {
        $this->update([
            'etape_actuelle' => self::ETAPE_REJETE,
            'statut' => self::STATUT_REJETE,
            'motif_rejet' => $motif,
            'user_assigned_id' => $user->id
        ]);
    }

    public function suspendre(User $user, string $motif): void
    {
        $this->update([
            'statut' => self::STATUT_SUSPENDU,
            'observations' => ($this->observations ? $this->observations . "\n\n" : '') .
                            "Suspendu le " . now()->format('d/m/Y') . " : " . $motif,
            'user_assigned_id' => $user->id
        ]);
    }

    public function reprendre(User $user): void
    {
        $this->update([
            'statut' => self::STATUT_EN_COURS,
            'user_assigned_id' => $user->id
        ]);
    }

    private function getEtapeSuivante(): ?string
    {
        return match($this->etape_actuelle) {
            self::ETAPE_RECEPTION => self::ETAPE_VERIFICATION,
            self::ETAPE_VERIFICATION => self::ETAPE_CALCUL,
            self::ETAPE_CALCUL => self::ETAPE_VALIDATION,
            self::ETAPE_VALIDATION => self::ETAPE_LIQUIDATION,
            self::ETAPE_LIQUIDATION => self::ETAPE_PAIEMENT,
            self::ETAPE_PAIEMENT => self::ETAPE_TERMINE,
            default => null
        };
    }

    private function marquerDateEtape(string $etape): void
    {
        $champDate = match($etape) {
            self::ETAPE_RECEPTION => 'date_reception',
            self::ETAPE_VERIFICATION => 'date_verification',
            self::ETAPE_CALCUL => 'date_calcul',
            self::ETAPE_VALIDATION => 'date_validation',
            self::ETAPE_LIQUIDATION => 'date_liquidation',
            self::ETAPE_PAIEMENT => 'date_paiement',
            default => null
        };

        if ($champDate) {
            $this->{$champDate} = now();
        }
    }

    /**
     * Générer un numéro de dossier unique
     */
    public static function generateNumeroDossier(): string
    {
        $annee = date('Y');
        $dernierNumero = self::where('numero_dossier', 'like', "PL{$annee}%")
                            ->orderBy('numero_dossier', 'desc')
                            ->first();

        if ($dernierNumero) {
            $numero = intval(substr($dernierNumero->numero_dossier, -4)) + 1;
        } else {
            $numero = 1;
        }

        return "PL{$annee}" . str_pad($numero, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get route key name for Laravel
     */
    public function getRouteKeyName(): string
    {
        return 'id';
    }
}
