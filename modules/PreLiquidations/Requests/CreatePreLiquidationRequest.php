<?php

namespace Modules\PreLiquidations\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Modules\PreLiquidations\Models\PreLiquidation;

class CreatePreLiquidationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // Informations du bénéficiaire
            'nom_beneficiaire' => 'required|string|max:255',
            'prenom_beneficiaire' => 'required|string|max:255',
            'date_naissance' => 'required|date|before:today',
            'lieu_naissance' => 'nullable|string|max:255',
            'sexe' => 'required|in:M,F',
            'numero_securite_sociale' => 'nullable|string|max:15|regex:/^[0-9\s]*$/',
            'numero_adherent' => 'nullable|string|max:50',
            
            // Informations de contact
            'adresse' => 'nullable|string|max:500',
            'telephone' => 'nullable|string|max:20|regex:/^[0-9\s\-\+\(\)]*$/',
            'email' => 'nullable|email|max:255',
            
            // Type de pension et dates
            'type_pension' => 'required|in:vieillesse,anticipee,invalidite,survivant,orphelin',
            'date_depart_retraite' => 'required|date|after:date_naissance',
            'date_fin_activite' => 'nullable|date|before_or_equal:date_depart_retraite',
            
            // Informations professionnelles
            'dernier_employeur' => 'nullable|string|max:255',
            'fonction' => 'nullable|string|max:255',
            'duree_cotisation_mois' => 'nullable|integer|min:0|max:600', // Max 50 ans
            'salaire_reference' => 'nullable|numeric|min:0|max:999999999.99',
            
            // Priorité et observations
            'priorite' => 'nullable|in:normale,haute,urgente',
            'observations' => 'nullable|string|max:2000',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'nom_beneficiaire.required' => 'Le nom du bénéficiaire est obligatoire.',
            'nom_beneficiaire.max' => 'Le nom du bénéficiaire ne peut pas dépasser 255 caractères.',
            
            'prenom_beneficiaire.required' => 'Le prénom du bénéficiaire est obligatoire.',
            'prenom_beneficiaire.max' => 'Le prénom du bénéficiaire ne peut pas dépasser 255 caractères.',
            
            'date_naissance.required' => 'La date de naissance est obligatoire.',
            'date_naissance.date' => 'La date de naissance doit être une date valide.',
            'date_naissance.before' => 'La date de naissance doit être antérieure à aujourd\'hui.',
            
            'lieu_naissance.max' => 'Le lieu de naissance ne peut pas dépasser 255 caractères.',
            
            'sexe.required' => 'Le sexe est obligatoire.',
            'sexe.in' => 'Le sexe doit être M (Masculin) ou F (Féminin).',
            
            'numero_securite_sociale.regex' => 'Le numéro de sécurité sociale ne doit contenir que des chiffres.',
            'numero_securite_sociale.max' => 'Le numéro de sécurité sociale ne peut pas dépasser 15 caractères.',
            
            'numero_adherent.max' => 'Le numéro d\'adhérent ne peut pas dépasser 50 caractères.',
            
            'adresse.max' => 'L\'adresse ne peut pas dépasser 500 caractères.',
            
            'telephone.regex' => 'Le numéro de téléphone n\'est pas valide.',
            'telephone.max' => 'Le numéro de téléphone ne peut pas dépasser 20 caractères.',
            
            'email.email' => 'L\'adresse email n\'est pas valide.',
            'email.max' => 'L\'adresse email ne peut pas dépasser 255 caractères.',
            
            'type_pension.required' => 'Le type de pension est obligatoire.',
            'type_pension.in' => 'Le type de pension sélectionné n\'est pas valide.',
            
            'date_depart_retraite.required' => 'La date de départ à la retraite est obligatoire.',
            'date_depart_retraite.date' => 'La date de départ à la retraite doit être une date valide.',
            'date_depart_retraite.after' => 'La date de départ à la retraite doit être postérieure à la date de naissance.',
            
            'date_fin_activite.date' => 'La date de fin d\'activité doit être une date valide.',
            'date_fin_activite.before_or_equal' => 'La date de fin d\'activité doit être antérieure ou égale à la date de départ à la retraite.',
            
            'dernier_employeur.max' => 'Le dernier employeur ne peut pas dépasser 255 caractères.',
            'fonction.max' => 'La fonction ne peut pas dépasser 255 caractères.',
            
            'duree_cotisation_mois.integer' => 'La durée de cotisation doit être un nombre entier.',
            'duree_cotisation_mois.min' => 'La durée de cotisation ne peut pas être négative.',
            'duree_cotisation_mois.max' => 'La durée de cotisation ne peut pas dépasser 600 mois (50 ans).',
            
            'salaire_reference.numeric' => 'Le salaire de référence doit être un nombre.',
            'salaire_reference.min' => 'Le salaire de référence ne peut pas être négatif.',
            'salaire_reference.max' => 'Le salaire de référence ne peut pas dépasser 999 999 999,99 €.',
            
            'priorite.in' => 'La priorité sélectionnée n\'est pas valide.',
            
            'observations.max' => 'Les observations ne peuvent pas dépasser 2000 caractères.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'nom_beneficiaire' => 'nom du bénéficiaire',
            'prenom_beneficiaire' => 'prénom du bénéficiaire',
            'date_naissance' => 'date de naissance',
            'lieu_naissance' => 'lieu de naissance',
            'sexe' => 'sexe',
            'numero_securite_sociale' => 'numéro de sécurité sociale',
            'numero_adherent' => 'numéro d\'adhérent',
            'adresse' => 'adresse',
            'telephone' => 'téléphone',
            'email' => 'email',
            'type_pension' => 'type de pension',
            'date_depart_retraite' => 'date de départ à la retraite',
            'date_fin_activite' => 'date de fin d\'activité',
            'dernier_employeur' => 'dernier employeur',
            'fonction' => 'fonction',
            'duree_cotisation_mois' => 'durée de cotisation',
            'salaire_reference' => 'salaire de référence',
            'priorite' => 'priorité',
            'observations' => 'observations',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Vérifier l'âge minimum pour la retraite
            if ($this->date_naissance && $this->date_depart_retraite) {
                $dateNaissance = \Carbon\Carbon::parse($this->date_naissance);
                $dateDepartRetraite = \Carbon\Carbon::parse($this->date_depart_retraite);
                $ageAuDepart = $dateNaissance->diffInYears($dateDepartRetraite);
                
                // Âge minimum selon le type de pension
                $ageMinimum = match($this->type_pension) {
                    'anticipee' => 55,
                    'invalidite' => 18,
                    'survivant' => 18,
                    'orphelin' => 0,
                    default => 60 // vieillesse
                };
                
                if ($ageAuDepart < $ageMinimum) {
                    $validator->errors()->add('date_depart_retraite', 
                        "L'âge au départ ({$ageAuDepart} ans) est inférieur à l'âge minimum requis ({$ageMinimum} ans) pour ce type de pension.");
                }
            }
            
            // Vérifier la cohérence des dates
            if ($this->date_fin_activite && $this->date_depart_retraite) {
                $dateFin = \Carbon\Carbon::parse($this->date_fin_activite);
                $dateDepart = \Carbon\Carbon::parse($this->date_depart_retraite);
                
                if ($dateFin->gt($dateDepart)) {
                    $validator->errors()->add('date_fin_activite', 
                        'La date de fin d\'activité ne peut pas être postérieure à la date de départ à la retraite.');
                }
            }
            
            // Validation spécifique selon le type de pension
            if ($this->type_pension === 'survivant' || $this->type_pension === 'orphelin') {
                // Pour les pensions de survivant/orphelin, certains champs peuvent être optionnels
                // Logique spécifique à implémenter selon les règles métier
            }
        });
    }
}
