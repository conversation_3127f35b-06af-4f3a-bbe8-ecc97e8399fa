<?php

namespace Modules\Cotisations\Repositories;

use App\Repositories\BaseRepository;
use App\Contracts\RepositoryInterface;
use Modules\Cotisations\Models\Cotisation;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class CotisationRepository extends BaseRepository implements RepositoryInterface
{
    /**
     * Constructor
     */
    public function __construct(Cotisation $model)
    {
        parent::__construct($model);
    }

    /**
     * Get paginated cotisations with filters
     */
    public function getPaginatedCotisations(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->with(['adherent', 'creator']);

        // Apply filters
        if (!empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('numero_cotisation', 'LIKE', "%{$filters['search']}%")
                  ->orWhere('periode_cotisation', 'LIKE', "%{$filters['search']}%")
                  ->orWhere('reference_paiement', 'LIKE', "%{$filters['search']}%")
                  ->orWhereHas('adherent', function ($subQ) use ($filters) {
                      $subQ->where('nom', 'LIKE', "%{$filters['search']}%")
                           ->orWhere('prenoms', 'LIKE', "%{$filters['search']}%")
                           ->orWhere('numero_adherent', 'LIKE', "%{$filters['search']}%");
                  });
            });
        }

        if (!empty($filters['statut'])) {
            $query->where('statut', $filters['statut']);
        }

        if (!empty($filters['annee'])) {
            $query->byYear($filters['annee']);
        }

        if (!empty($filters['mois'])) {
            $query->byMonth($filters['mois']);
        }

        if (!empty($filters['adherent_id'])) {
            $query->where('adherent_id', $filters['adherent_id']);
        }

        if (!empty($filters['date_from'])) {
            $query->where('date_echeance', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('date_echeance', '<=', $filters['date_to']);
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Get overdue cotisations
     */
    public function getOverdueCotisations(): Collection
    {
        return $this->model->overdue()
                          ->with(['adherent'])
                          ->orderBy('date_echeance', 'asc')
                          ->get();
    }

    /**
     * Get cotisations by period
     */
    public function getCotisationsByPeriod(string $period): Collection
    {
        return $this->model->byPeriod($period)
                          ->with(['adherent'])
                          ->get();
    }

    /**
     * Get cotisations statistics
     */
    public function getStatistics(): array
    {
        $currentYear = date('Y');
        $currentMonth = date('n');

        return [
            'total' => $this->model->count(),
            'payees' => $this->model->paid()->count(),
            'en_attente' => $this->model->pending()->count(),
            'en_retard' => $this->model->overdue()->count(),
            'montant_total_annee' => $this->model->byYear($currentYear)->sum('montant_cotisation'),
            'montant_paye_annee' => $this->model->byYear($currentYear)->paid()->sum('montant_cotisation'),
            'montant_total_mois' => $this->model->byYear($currentYear)->byMonth($currentMonth)->sum('montant_cotisation'),
            'montant_paye_mois' => $this->model->byYear($currentYear)->byMonth($currentMonth)->paid()->sum('montant_cotisation'),
        ];
    }

    /**
     * Get monthly statistics for chart
     */
    public function getMonthlyStatistics(int $year): array
    {
        $statistics = [];
        
        for ($month = 1; $month <= 12; $month++) {
            $cotisations = $this->model->byYear($year)->byMonth($month);
            
            $statistics[] = [
                'month' => $month,
                'total_cotisations' => $cotisations->sum('montant_cotisation'),
                'cotisations_payees' => $cotisations->paid()->sum('montant_cotisation'),
                'nombre_cotisations' => $cotisations->count(),
                'nombre_payees' => $cotisations->paid()->count(),
            ];
        }

        return $statistics;
    }

    /**
     * Get cotisations by adherent
     */
    public function getCotisationsByAdherent(int $adherentId): Collection
    {
        return $this->model->where('adherent_id', $adherentId)
                          ->orderBy('periode_cotisation', 'desc')
                          ->get();
    }

    /**
     * Get recent cotisations
     */
    public function getRecentCotisations(int $days = 30): Collection
    {
        return $this->model->where('created_at', '>=', now()->subDays($days))
                          ->with(['adherent'])
                          ->orderBy('created_at', 'desc')
                          ->get();
    }

    /**
     * Generate next cotisation number
     */
    public function generateNextNumero(): string
    {
        return Cotisation::generateNextNumero();
    }

    /**
     * Get cotisations for relance
     */
    public function getCotisationsForRelance(): Collection
    {
        return $this->model->overdue()
                          ->with(['adherent', 'relances'])
                          ->get()
                          ->filter(function ($cotisation) {
                              // Only cotisations without recent relances (last 7 days)
                              $lastRelance = $cotisation->relances()->latest()->first();
                              return !$lastRelance || $lastRelance->created_at->diffInDays(now()) >= 7;
                          });
    }

    /**
     * Get payment summary by mode
     */
    public function getPaymentSummaryByMode(): array
    {
        return $this->model->paid()
                          ->selectRaw('mode_paiement, COUNT(*) as count, SUM(montant_cotisation) as total')
                          ->groupBy('mode_paiement')
                          ->get()
                          ->toArray();
    }

    /**
     * Get top paying adherents
     */
    public function getTopPayingAdherents(int $limit = 10): Collection
    {
        return $this->model->paid()
                          ->with(['adherent'])
                          ->selectRaw('adherent_id, SUM(montant_cotisation) as total_paye, COUNT(*) as nombre_cotisations')
                          ->groupBy('adherent_id')
                          ->orderBy('total_paye', 'desc')
                          ->limit($limit)
                          ->get();
    }

    /**
     * Get cotisations for export
     */
    public function getCotisationsForExport(array $filters = []): Collection
    {
        $query = $this->model->with(['adherent']);

        // Apply same filters as pagination
        if (!empty($filters['statut'])) {
            $query->where('statut', $filters['statut']);
        }

        if (!empty($filters['annee'])) {
            $query->byYear($filters['annee']);
        }

        if (!empty($filters['mois'])) {
            $query->byMonth($filters['mois']);
        }

        return $query->orderBy('periode_cotisation', 'desc')->get();
    }

    /**
     * Calculate total cotisations for adherent
     */
    public function getTotalCotisationsForAdherent(int $adherentId): array
    {
        $cotisations = $this->model->where('adherent_id', $adherentId);

        return [
            'total_cotisations' => $cotisations->sum('montant_cotisation'),
            'total_payees' => $cotisations->paid()->sum('montant_cotisation'),
            'total_en_attente' => $cotisations->pending()->sum('montant_cotisation'),
            'nombre_cotisations' => $cotisations->count(),
            'nombre_payees' => $cotisations->paid()->count(),
            'derniere_cotisation' => $cotisations->latest()->first(),
        ];
    }
}
