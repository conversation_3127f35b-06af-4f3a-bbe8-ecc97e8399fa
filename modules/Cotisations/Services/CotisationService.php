<?php

namespace Modules\Cotisations\Services;

use Modules\Cotisations\Models\Cotisation;
use Modules\Cotisations\Repositories\CotisationRepository;
use Modules\Adherents\Models\Adherent;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class CotisationService
{
    protected CotisationRepository $cotisationRepository;

    public function __construct(CotisationRepository $cotisationRepository)
    {
        $this->cotisationRepository = $cotisationRepository;
    }

    /**
     * Create new cotisation
     */
    public function createCotisation(array $data): Cotisation
    {
        DB::beginTransaction();
        
        try {
            // Generate cotisation number if not provided
            if (empty($data['numero_cotisation'])) {
                $data['numero_cotisation'] = $this->cotisationRepository->generateNextNumero();
            }

            // Set created_by
            $data['created_by'] = Auth::id();

            // Create cotisation
            $cotisation = $this->cotisationRepository->create($data);

            // Calculate amounts
            $cotisation->calculateAmounts();
            $cotisation->save();

            // Create echeancier if needed
            if (!empty($data['create_echeancier'])) {
                $this->createEcheancier($cotisation, $data['echeancier_data'] ?? []);
            }

            DB::commit();

            Log::info('Cotisation created', [
                'cotisation_id' => $cotisation->id,
                'numero_cotisation' => $cotisation->numero_cotisation,
                'adherent_id' => $cotisation->adherent_id,
                'created_by' => Auth::id()
            ]);

            return $cotisation;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating cotisation', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Update cotisation
     */
    public function updateCotisation(Cotisation $cotisation, array $data): bool
    {
        DB::beginTransaction();
        
        try {
            $result = $this->cotisationRepository->update($cotisation, $data);

            // Recalculate amounts if base data changed
            if (isset($data['montant_base']) || isset($data['taux_cotisation'])) {
                $cotisation->calculateAmounts();
                $cotisation->save();
            }

            DB::commit();

            if ($result) {
                Log::info('Cotisation updated', [
                    'cotisation_id' => $cotisation->id,
                    'numero_cotisation' => $cotisation->numero_cotisation,
                    'updated_by' => Auth::id()
                ]);
            }

            return $result;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating cotisation', [
                'cotisation_id' => $cotisation->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Mark cotisation as paid
     */
    public function markAsPaid(Cotisation $cotisation, string $paymentMode, string $reference = null): bool
    {
        try {
            $cotisation->markAsPaid($paymentMode, $reference);

            Log::info('Cotisation marked as paid', [
                'cotisation_id' => $cotisation->id,
                'numero_cotisation' => $cotisation->numero_cotisation,
                'payment_mode' => $paymentMode,
                'reference' => $reference,
                'marked_by' => Auth::id()
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Error marking cotisation as paid', [
                'cotisation_id' => $cotisation->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Cancel cotisation
     */
    public function cancelCotisation(Cotisation $cotisation, string $reason = null): bool
    {
        try {
            $data = [
                'statut' => Cotisation::STATUS_ANNULEE,
                'observations' => $reason ? "Annulée: {$reason}" : 'Annulée'
            ];

            $result = $this->cotisationRepository->update($cotisation, $data);

            if ($result) {
                Log::info('Cotisation cancelled', [
                    'cotisation_id' => $cotisation->id,
                    'reason' => $reason,
                    'cancelled_by' => Auth::id()
                ]);
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('Error cancelling cotisation', [
                'cotisation_id' => $cotisation->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Generate cotisations for period
     */
    public function generateCotisationsForPeriod(string $period, array $adherentIds = []): array
    {
        $results = [
            'success' => 0,
            'errors' => []
        ];

        DB::beginTransaction();

        try {
            // Get adherents
            $adherents = empty($adherentIds) 
                ? Adherent::active()->get() 
                : Adherent::whereIn('id', $adherentIds)->get();

            foreach ($adherents as $adherent) {
                try {
                    // Check if cotisation already exists for this period
                    $existingCotisation = $this->cotisationRepository
                        ->where(['adherent_id' => $adherent->id, 'periode_cotisation' => $period])
                        ->first();

                    if ($existingCotisation) {
                        $results['errors'][] = [
                            'adherent' => $adherent->full_name,
                            'error' => 'Cotisation déjà existante pour cette période'
                        ];
                        continue;
                    }

                    // Create cotisation
                    $cotisationData = [
                        'adherent_id' => $adherent->id,
                        'periode_cotisation' => $period,
                        'annee_cotisation' => (int) substr($period, 0, 4),
                        'mois_cotisation' => (int) substr($period, 5, 2),
                        'montant_base' => $adherent->salaire_base ?? 0,
                        'taux_cotisation' => 12.5, // Default rate, should be configurable
                        'date_echeance' => now()->addMonth()->endOfMonth(),
                        'statut' => Cotisation::STATUS_EN_ATTENTE,
                        'created_by' => Auth::id()
                    ];

                    $this->createCotisation($cotisationData);
                    $results['success']++;

                } catch (\Exception $e) {
                    $results['errors'][] = [
                        'adherent' => $adherent->full_name,
                        'error' => $e->getMessage()
                    ];
                }
            }

            DB::commit();

            Log::info('Cotisations generated for period', [
                'period' => $period,
                'success_count' => $results['success'],
                'error_count' => count($results['errors']),
                'generated_by' => Auth::id()
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error generating cotisations for period', [
                'period' => $period,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }

        return $results;
    }

    /**
     * Get cotisations statistics
     */
    public function getStatistics(): array
    {
        return $this->cotisationRepository->getStatistics();
    }

    /**
     * Get overdue cotisations
     */
    public function getOverdueCotisations()
    {
        return $this->cotisationRepository->getOverdueCotisations();
    }

    /**
     * Process automatic relances
     */
    public function processAutomaticRelances(): array
    {
        $cotisations = $this->cotisationRepository->getCotisationsForRelance();
        $results = [
            'processed' => 0,
            'errors' => []
        ];

        foreach ($cotisations as $cotisation) {
            try {
                $this->createRelance($cotisation);
                $results['processed']++;
            } catch (\Exception $e) {
                $results['errors'][] = [
                    'cotisation' => $cotisation->numero_cotisation,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * Create echeancier for cotisation
     */
    private function createEcheancier(Cotisation $cotisation, array $echeancierData): void
    {
        $numberOfInstallments = $echeancierData['number_of_installments'] ?? 1;
        $installmentAmount = $cotisation->montant_cotisation / $numberOfInstallments;

        for ($i = 1; $i <= $numberOfInstallments; $i++) {
            $cotisation->echeanciers()->create([
                'adherent_id' => $cotisation->adherent_id,
                'numero_echeance' => $cotisation->numero_cotisation . '-' . str_pad($i, 2, '0', STR_PAD_LEFT),
                'montant_echeance' => $installmentAmount,
                'date_echeance' => $cotisation->date_echeance->addMonths($i - 1),
                'statut' => 'en_attente'
            ]);
        }
    }

    /**
     * Create relance for cotisation
     */
    private function createRelance(Cotisation $cotisation): void
    {
        $relanceCount = $cotisation->relances()->count();
        
        $type = match($relanceCount) {
            0 => 'premiere',
            1 => 'deuxieme',
            2 => 'derniere',
            default => 'mise_en_demeure'
        };

        $message = $this->generateRelanceMessage($cotisation, $type);

        $cotisation->relances()->create([
            'adherent_id' => $cotisation->adherent_id,
            'type_relance' => $type,
            'date_relance' => now(),
            'canal_envoi' => 'email', // Default, should be configurable
            'message' => $message,
            'statut' => 'programmee',
            'created_by' => Auth::id()
        ]);
    }

    /**
     * Generate relance message
     */
    private function generateRelanceMessage(Cotisation $cotisation, string $type): string
    {
        $adherent = $cotisation->adherent;
        
        return match($type) {
            'premiere' => "Cher(e) {$adherent->full_name}, votre cotisation {$cotisation->numero_cotisation} d'un montant de {$cotisation->formatted_montant} est en retard. Merci de régulariser votre situation.",
            'deuxieme' => "Cher(e) {$adherent->full_name}, malgré notre premier rappel, votre cotisation {$cotisation->numero_cotisation} reste impayée. Merci de procéder au paiement rapidement.",
            'derniere' => "Cher(e) {$adherent->full_name}, ceci est notre dernier rappel concernant votre cotisation {$cotisation->numero_cotisation}. Merci de régulariser immédiatement.",
            'mise_en_demeure' => "Cher(e) {$adherent->full_name}, nous vous mettons en demeure de payer votre cotisation {$cotisation->numero_cotisation} sous 15 jours.",
            default => "Rappel de cotisation {$cotisation->numero_cotisation}"
        };
    }

    /**
     * Export cotisations data
     */
    public function exportCotisations(array $filters = []): array
    {
        $cotisations = $this->cotisationRepository->getCotisationsForExport($filters);
        
        return $cotisations->map(function ($cotisation) {
            return [
                'Numéro Cotisation' => $cotisation->numero_cotisation,
                'Adhérent' => $cotisation->adherent->full_name,
                'Numéro Adhérent' => $cotisation->adherent->numero_adherent,
                'Période' => $cotisation->periode_cotisation,
                'Montant Base' => $cotisation->montant_base,
                'Taux' => $cotisation->taux_cotisation . '%',
                'Montant Cotisation' => $cotisation->montant_cotisation,
                'Date Échéance' => $cotisation->date_echeance->format('d/m/Y'),
                'Date Paiement' => $cotisation->date_paiement?->format('d/m/Y H:i'),
                'Mode Paiement' => $cotisation->payment_mode_label,
                'Référence' => $cotisation->reference_paiement,
                'Statut' => $cotisation->status_label,
            ];
        })->toArray();
    }
}
