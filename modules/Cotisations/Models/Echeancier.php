<?php

namespace Modules\Cotisations\Models;

use App\Models\BaseModel;
use Modules\Adherents\Models\Adherent;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Echeancier extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'adherent_id',
        'cotisation_id',
        'numero_echeance',
        'montant_echeance',
        'date_echeance',
        'date_paiement',
        'statut',
        'observations'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'montant_echeance' => 'decimal:2',
        'date_echeance' => 'date',
        'date_paiement' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Status constants
     */
    const STATUS_EN_ATTENTE = 'en_attente';
    const STATUS_PAYEE = 'payee';
    const STATUS_EN_RETARD = 'en_retard';

    /**
     * Get adherent that owns this echeancier
     */
    public function adherent(): BelongsTo
    {
        return $this->belongsTo(Adherent::class);
    }

    /**
     * Get cotisation that owns this echeancier
     */
    public function cotisation(): BelongsTo
    {
        return $this->belongsTo(Cotisation::class);
    }

    /**
     * Get formatted montant
     */
    public function getFormattedMontantAttribute(): string
    {
        return number_format($this->montant_echeance, 0, ',', ' ') . ' €';
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->statut) {
            self::STATUS_EN_ATTENTE => 'En attente',
            self::STATUS_PAYEE => 'Payée',
            self::STATUS_EN_RETARD => 'En retard',
            default => 'Inconnu'
        };
    }

    /**
     * Check if echeance is overdue
     */
    public function isOverdue(): bool
    {
        return $this->statut !== self::STATUS_PAYEE && 
               $this->date_echeance < now()->startOfDay();
    }

    /**
     * Scope for overdue echeances
     */
    public function scopeOverdue($query)
    {
        return $query->where('statut', '!=', self::STATUS_PAYEE)
                    ->where('date_echeance', '<', now()->startOfDay());
    }

    /**
     * Scope for paid echeances
     */
    public function scopePaid($query)
    {
        return $query->where('statut', self::STATUS_PAYEE);
    }

    /**
     * Mark as paid
     */
    public function markAsPaid(): void
    {
        $this->update([
            'statut' => self::STATUS_PAYEE,
            'date_paiement' => now(),
        ]);
    }
}
