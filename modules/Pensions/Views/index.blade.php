@extends('layouts.template')

@section('title', 'Gestion des Pensions - CRFM')

@section('page-header')
@section('page-title', 'Gestion des Pensions')
@section('page-description', 'Pré-liquidation et gestion des dossiers de pension')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Pensions</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <!-- Statistiques rapides -->
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-yellow text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($statistics['en_cours'] ?? 42) }}</h4>
                        <h6 class="text-white m-b-0">En cours</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-briefcase f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-green text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($statistics['pretes'] ?? 28) }}</h4>
                        <h6 class="text-white m-b-0">Prêtes</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-check-circle f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-blue text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($statistics['liquidees'] ?? 156) }}</h4>
                        <h6 class="text-white m-b-0">Liquidées</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-award f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-purple text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">8.2M €</h4>
                        <h6 class="text-white m-b-0">Montant total</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-dollar-sign f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Workflow de pré-liquidation -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-git-branch text-c-blue me-2"></i>Workflow de pré-liquidation</h5>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-md-2 text-center">
                        <div class="workflow-step">
                            <div class="step-icon bg-c-green">
                                <i class="feather icon-file-plus text-white"></i>
                            </div>
                            <h6 class="m-t-10">Réception</h6>
                            <p class="text-muted">Dossier reçu</p>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="workflow-step">
                            <div class="step-icon bg-c-blue">
                                <i class="feather icon-check-square text-white"></i>
                            </div>
                            <h6 class="m-t-10">Vérification</h6>
                            <p class="text-muted">Documents OK</p>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="workflow-step">
                            <div class="step-icon bg-c-yellow">
                                <i class="feather icon-calculator text-white"></i>
                            </div>
                            <h6 class="m-t-10">Calcul</h6>
                            <p class="text-muted">Montants</p>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="workflow-step">
                            <div class="step-icon bg-c-purple">
                                <i class="feather icon-user-check text-white"></i>
                            </div>
                            <h6 class="m-t-10">Validation</h6>
                            <p class="text-muted">Contrôle</p>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="workflow-step">
                            <div class="step-icon bg-c-red">
                                <i class="feather icon-file-text text-white"></i>
                            </div>
                            <h6 class="m-t-10">Liquidation</h6>
                            <p class="text-muted">Finalisation</p>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="workflow-step">
                            <div class="step-icon bg-c-green">
                                <i class="feather icon-credit-card text-white"></i>
                            </div>
                            <h6 class="m-t-10">Paiement</h6>
                            <p class="text-muted">Versement</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Filtres et recherche -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-filter text-c-blue me-2"></i>Filtres et recherche</h5>
                <div class="card-header-right">
                    <a href="{{ route('pensions.create') }}" class="btn btn-primary btn-sm">
                        <i class="feather icon-plus me-1"></i>Nouveau dossier
                    </a>
                </div>
            </div>
            <div class="card-block">
                <form method="GET" action="{{ route('pensions.index') }}">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Recherche</label>
                                <input type="text" name="search" class="form-control" 
                                       placeholder="Nom, numéro dossier..." 
                                       value="{{ $filters['search'] ?? '' }}">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Statut</label>
                                <select name="statut" class="form-control">
                                    <option value="">Tous</option>
                                    <option value="en_cours" {{ ($filters['statut'] ?? '') === 'en_cours' ? 'selected' : '' }}>En cours</option>
                                    <option value="prete" {{ ($filters['statut'] ?? '') === 'prete' ? 'selected' : '' }}>Prête</option>
                                    <option value="liquidee" {{ ($filters['statut'] ?? '') === 'liquidee' ? 'selected' : '' }}>Liquidée</option>
                                    <option value="suspendue" {{ ($filters['statut'] ?? '') === 'suspendue' ? 'selected' : '' }}>Suspendue</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Type</label>
                                <select name="type_pension" class="form-control">
                                    <option value="">Tous</option>
                                    <option value="vieillesse" {{ ($filters['type_pension'] ?? '') === 'vieillesse' ? 'selected' : '' }}>Vieillesse</option>
                                    <option value="anticipee" {{ ($filters['type_pension'] ?? '') === 'anticipee' ? 'selected' : '' }}>Anticipée</option>
                                    <option value="invalidite" {{ ($filters['type_pension'] ?? '') === 'invalidite' ? 'selected' : '' }}>Invalidité</option>
                                    <option value="survivant" {{ ($filters['type_pension'] ?? '') === 'survivant' ? 'selected' : '' }}>Survivant</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Année</label>
                                <select name="annee" class="form-control">
                                    <option value="">Toutes</option>
                                    @for($year = date('Y'); $year >= date('Y') - 3; $year--)
                                        <option value="{{ $year }}" {{ ($filters['annee'] ?? '') == $year ? 'selected' : '' }}>{{ $year }}</option>
                                    @endfor
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div class="btn-group btn-block">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="feather icon-search"></i>
                                    </button>
                                    <a href="{{ route('pensions.index') }}" class="btn btn-secondary">
                                        <i class="feather icon-refresh-cw"></i>
                                    </a>
                                    <a href="{{ route('pensions.export', $filters) }}" class="btn btn-success">
                                        <i class="feather icon-download"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Liste des dossiers -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-list text-c-blue me-2"></i>Dossiers de pension</h5>
                <div class="card-header-right">
                    <span class="badge bg-info">{{ $dossiers->total() ?? 0 }} résultat(s)</span>
                </div>
            </div>
            <div class="card-block">
                @if(($dossiers->count() ?? 0) > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>N° Dossier</th>
                                <th>Bénéficiaire</th>
                                <th>Type Pension</th>
                                <th>Date Départ</th>
                                <th>Montant Calculé</th>
                                <th>Étape Actuelle</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($dossiers ?? [] as $dossier)
                            <tr>
                                <td>
                                    <strong>{{ $dossier->numero_dossier ?? 'PL2024001' }}</strong>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ $dossier->adherent->full_name ?? 'DIARRA Moussa' }}</strong>
                                        <br><small class="text-muted">{{ $dossier->adherent->numero_adherent ?? 'ADH2024001' }}</small>
                                    </div>
                                </td>
                                <td>{{ ucfirst($dossier->type_pension ?? 'vieillesse') }}</td>
                                <td>{{ $dossier->date_depart_retraite?->format('d/m/Y') ?? '01/03/2024' }}</td>
                                <td>
                                    <strong>{{ $dossier->formatted_montant ?? '285,000 €' }}</strong>
                                </td>
                                <td>{{ ucfirst($dossier->etape_actuelle ?? 'calcul') }}</td>
                                <td>
                                    <span class="badge bg-{{ $dossier->status_color ?? 'warning' }}">
                                        {{ $dossier->status_label ?? 'En cours' }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('pensions.show', $dossier->id ?? 1) }}" 
                                           class="btn btn-primary" title="Voir">
                                            <i class="feather icon-eye"></i>
                                        </a>
                                        <a href="{{ route('pensions.edit', $dossier->id ?? 1) }}" 
                                           class="btn btn-warning" title="Modifier">
                                            <i class="feather icon-edit"></i>
                                        </a>
                                        <button class="btn btn-success" 
                                                onclick="advanceWorkflow({{ $dossier->id ?? 1 }})" 
                                                title="Avancer étape">
                                            <i class="feather icon-play"></i>
                                        </button>
                                        <button class="btn btn-info" 
                                                onclick="calculatePension({{ $dossier->id ?? 1 }})" 
                                                title="Calculer">
                                            <i class="feather icon-calculator"></i>
                                        </button>
                                        <button class="btn btn-secondary" 
                                                onclick="printDossier({{ $dossier->id ?? 1 }})" 
                                                title="Imprimer">
                                            <i class="feather icon-printer"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <!-- Données d'exemple si pas de dossiers -->
                            <tr>
                                <td><strong>PL2024001</strong></td>
                                <td>
                                    <div>
                                        <strong>DIARRA Moussa</strong>
                                        <br><small class="text-muted">ADH2024001</small>
                                    </div>
                                </td>
                                <td>Vieillesse</td>
                                <td>01/03/2024</td>
                                <td><strong>285,000 €</strong></td>
                                <td>Calcul</td>
                                <td><span class="badge bg-warning">En cours</span></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-primary" title="Voir">
                                            <i class="feather icon-eye"></i>
                                        </button>
                                        <button class="btn btn-warning" title="Modifier">
                                            <i class="feather icon-edit"></i>
                                        </button>
                                        <button class="btn btn-success" title="Avancer étape">
                                            <i class="feather icon-play"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>PL2024002</strong></td>
                                <td>
                                    <div>
                                        <strong>KEITA Aminata</strong>
                                        <br><small class="text-muted">ADH2024002</small>
                                    </div>
                                </td>
                                <td>Anticipée</td>
                                <td>15/02/2024</td>
                                <td><strong>198,500 €</strong></td>
                                <td>Validation</td>
                                <td><span class="badge bg-success">Prête</span></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-primary" title="Voir">
                                            <i class="feather icon-eye"></i>
                                        </button>
                                        <button class="btn btn-warning" title="Modifier">
                                            <i class="feather icon-edit"></i>
                                        </button>
                                        <button class="btn btn-success" title="Liquider">
                                            <i class="feather icon-check"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                @else
                <div class="text-center py-4">
                    <i class="feather icon-briefcase f-40 text-muted"></i>
                    <h5 class="mt-3">Aucun dossier trouvé</h5>
                    <p class="text-muted">Aucun dossier de pension ne correspond aux critères de recherche.</p>
                    <a href="{{ route('pensions.create') }}" class="btn btn-primary">
                        <i class="feather icon-plus me-2"></i>Créer le premier dossier
                    </a>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.workflow-step {
    position: relative;
    margin-bottom: 20px;
}

.step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 20px;
}
</style>
@endpush

@push('scripts')
<script>
function advanceWorkflow(dossierId) {
    if (confirm('Avancer ce dossier à l\'étape suivante ?')) {
        fetch(`/pensions/${dossierId}/advance`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                CRFM.showToast('Dossier avancé avec succès', 'success');
                location.reload();
            } else {
                CRFM.showToast('Erreur lors de l\'avancement', 'error');
            }
        });
    }
}

function calculatePension(dossierId) {
    if (confirm('Recalculer le montant de la pension ?')) {
        fetch(`/pensions/${dossierId}/calculate`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                CRFM.showToast(`Montant calculé: ${data.montant} €`, 'success');
                location.reload();
            } else {
                CRFM.showToast('Erreur lors du calcul', 'error');
            }
        });
    }
}

function printDossier(dossierId) {
    window.open(`/pensions/${dossierId}/print`, '_blank');
}
</script>
@endpush
