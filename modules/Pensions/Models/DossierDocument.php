<?php

namespace Modules\Pensions\Models;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DossierDocument extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'dossier_pension_id',
        'type_document',
        'nom_document',
        'chemin_fichier',
        'taille_fichier',
        'type_mime',
        'date_upload',
        'uploaded_by',
        'is_required',
        'is_verified',
        'date_verification',
        'verified_by',
        'observations'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'date_upload' => 'datetime',
        'date_verification' => 'datetime',
        'is_required' => 'boolean',
        'is_verified' => 'boolean',
        'taille_fichier' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Document type constants
     */
    const TYPE_DEMANDE_PENSION = 'demande_pension';
    const TYPE_CERTIFICAT_TRAVAIL = 'certificat_travail';
    const TYPE_BULLETINS_SALAIRE = 'bulletins_salaire';
    const TYPE_ACTE_NAISSANCE = 'acte_naissance';
    const TYPE_CNI = 'cni';
    const TYPE_CERTIFICAT_MEDICAL = 'certificat_medical';
    const TYPE_ACTE_DECES = 'acte_deces'; // For survivor pensions
    const TYPE_CERTIFICAT_MARIAGE = 'certificat_mariage';
    const TYPE_RIB = 'rib';
    const TYPE_AUTRE = 'autre';

    /**
     * Get dossier pension that owns this document
     */
    public function dossierPension(): BelongsTo
    {
        return $this->belongsTo(DossierPension::class);
    }

    /**
     * Get user who uploaded this document
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'uploaded_by');
    }

    /**
     * Get user who verified this document
     */
    public function verifier(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'verified_by');
    }

    /**
     * Get document type label
     */
    public function getTypeLabelAttribute(): string
    {
        return match($this->type_document) {
            self::TYPE_DEMANDE_PENSION => 'Demande de pension',
            self::TYPE_CERTIFICAT_TRAVAIL => 'Certificat de travail',
            self::TYPE_BULLETINS_SALAIRE => 'Bulletins de salaire',
            self::TYPE_ACTE_NAISSANCE => 'Acte de naissance',
            self::TYPE_CNI => 'Carte Nationale d\'Identité',
            self::TYPE_CERTIFICAT_MEDICAL => 'Certificat médical',
            self::TYPE_ACTE_DECES => 'Acte de décès',
            self::TYPE_CERTIFICAT_MARIAGE => 'Certificat de mariage',
            self::TYPE_RIB => 'Relevé d\'Identité Bancaire',
            self::TYPE_AUTRE => 'Autre document',
            default => 'Document'
        };
    }

    /**
     * Get formatted file size
     */
    public function getFormattedSizeAttribute(): string
    {
        $bytes = $this->taille_fichier;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if document is an image
     */
    public function isImage(): bool
    {
        return str_starts_with($this->type_mime, 'image/');
    }

    /**
     * Check if document is a PDF
     */
    public function isPdf(): bool
    {
        return $this->type_mime === 'application/pdf';
    }

    /**
     * Scope for required documents
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    /**
     * Scope for verified documents
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope by document type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type_document', $type);
    }

    /**
     * Mark as verified
     */
    public function markAsVerified(int $verifiedBy, string $observations = null): void
    {
        $this->update([
            'is_verified' => true,
            'date_verification' => now(),
            'verified_by' => $verifiedBy,
            'observations' => $observations,
        ]);
    }

    /**
     * Get required documents for pension type
     */
    public static function getRequiredDocumentsForType(string $pensionType): array
    {
        $baseDocuments = [
            self::TYPE_DEMANDE_PENSION,
            self::TYPE_ACTE_NAISSANCE,
            self::TYPE_CNI,
            self::TYPE_CERTIFICAT_TRAVAIL,
            self::TYPE_BULLETINS_SALAIRE,
            self::TYPE_RIB,
        ];

        return match($pensionType) {
            DossierPension::TYPE_INVALIDITE => array_merge($baseDocuments, [
                self::TYPE_CERTIFICAT_MEDICAL
            ]),
            DossierPension::TYPE_SURVIVANT => array_merge($baseDocuments, [
                self::TYPE_ACTE_DECES,
                self::TYPE_CERTIFICAT_MARIAGE
            ]),
            default => $baseDocuments
        };
    }
}
