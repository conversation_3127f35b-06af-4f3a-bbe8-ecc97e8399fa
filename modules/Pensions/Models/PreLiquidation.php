<?php

namespace Modules\Pensions\Models;

use App\Models\BaseModel;
use Modules\Adherents\Models\Adherent;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PreLiquidation extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'dossier_pension_id',
        'adherent_id',
        'numero_pre_liquidation',
        'date_calcul',
        'salaire_reference',
        'duree_cotisation_mois',
        'taux_liquidation',
        'montant_brut',
        'montant_net',
        'retenues_fiscales',
        'retenues_sociales',
        'autres_retenues',
        'date_effet',
        'periodicite_versement',
        'mode_versement',
        'observations',
        'statut',
        'calculated_by',
        'validated_by',
        'validated_at'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'date_calcul' => 'date',
        'date_effet' => 'date',
        'salaire_reference' => 'decimal:2',
        'duree_cotisation_mois' => 'integer',
        'taux_liquidation' => 'decimal:4',
        'montant_brut' => 'decimal:2',
        'montant_net' => 'decimal:2',
        'retenues_fiscales' => 'decimal:2',
        'retenues_sociales' => 'decimal:2',
        'autres_retenues' => 'decimal:2',
        'validated_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Periodicity constants
     */
    const PERIODICITE_MENSUELLE = 'mensuelle';
    const PERIODICITE_TRIMESTRIELLE = 'trimestrielle';
    const PERIODICITE_SEMESTRIELLE = 'semestrielle';
    const PERIODICITE_ANNUELLE = 'annuelle';

    /**
     * Payment mode constants
     */
    const MODE_VIREMENT = 'virement';
    const MODE_CHEQUE = 'cheque';
    const MODE_ESPECES = 'especes';
    const MODE_MOBILE_MONEY = 'mobile_money';

    /**
     * Status constants
     */
    const STATUS_BROUILLON = 'brouillon';
    const STATUS_CALCULE = 'calcule';
    const STATUS_VALIDE = 'valide';
    const STATUS_REJETE = 'rejete';

    /**
     * Get dossier pension that owns this pre-liquidation
     */
    public function dossierPension(): BelongsTo
    {
        return $this->belongsTo(DossierPension::class);
    }

    /**
     * Get adherent that owns this pre-liquidation
     */
    public function adherent(): BelongsTo
    {
        return $this->belongsTo(Adherent::class);
    }

    /**
     * Get user who calculated this pre-liquidation
     */
    public function calculator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'calculated_by');
    }

    /**
     * Get user who validated this pre-liquidation
     */
    public function validator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'validated_by');
    }

    /**
     * Get periodicity label
     */
    public function getPeriodiciteLabelAttribute(): string
    {
        return match($this->periodicite_versement) {
            self::PERIODICITE_MENSUELLE => 'Mensuelle',
            self::PERIODICITE_TRIMESTRIELLE => 'Trimestrielle',
            self::PERIODICITE_SEMESTRIELLE => 'Semestrielle',
            self::PERIODICITE_ANNUELLE => 'Annuelle',
            default => 'Non définie'
        };
    }

    /**
     * Get payment mode label
     */
    public function getModeLabelAttribute(): string
    {
        return match($this->mode_versement) {
            self::MODE_VIREMENT => 'Virement bancaire',
            self::MODE_CHEQUE => 'Chèque',
            self::MODE_ESPECES => 'Espèces',
            self::MODE_MOBILE_MONEY => 'Mobile Money',
            default => 'Non défini'
        };
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->statut) {
            self::STATUS_BROUILLON => 'Brouillon',
            self::STATUS_CALCULE => 'Calculé',
            self::STATUS_VALIDE => 'Validé',
            self::STATUS_REJETE => 'Rejeté',
            default => 'Statut inconnu'
        };
    }

    /**
     * Get formatted amounts
     */
    public function getFormattedMontantBrutAttribute(): string
    {
        return number_format($this->montant_brut, 0, ',', ' ') . ' €';
    }

    public function getFormattedMontantNetAttribute(): string
    {
        return number_format($this->montant_net, 0, ',', ' ') . ' €';
    }

    /**
     * Get years of contribution
     */
    public function getYearsOfContributionAttribute(): float
    {
        return round($this->duree_cotisation_mois / 12, 2);
    }

    /**
     * Get total retenues
     */
    public function getTotalRetenuesAttribute(): float
    {
        return $this->retenues_fiscales + $this->retenues_sociales + $this->autres_retenues;
    }

    /**
     * Calculate net amount from gross amount and retenues
     */
    public function calculateNetAmount(): void
    {
        $this->montant_net = $this->montant_brut - $this->total_retenues;
    }

    /**
     * Calculate retenues based on gross amount
     */
    public function calculateRetenues(): void
    {
        // Simplified calculation - real calculation would be more complex
        
        // Fiscal retenues (example: 10% if above threshold)
        $fiscalThreshold = 100000; // 100,000 €
        $this->retenues_fiscales = $this->montant_brut > $fiscalThreshold 
            ? $this->montant_brut * 0.10 
            : 0;

        // Social retenues (example: 5%)
        $this->retenues_sociales = $this->montant_brut * 0.05;

        // Other retenues (can be set manually)
        $this->autres_retenues = $this->autres_retenues ?? 0;

        // Calculate net amount
        $this->calculateNetAmount();
    }

    /**
     * Scope for validated pre-liquidations
     */
    public function scopeValidated($query)
    {
        return $query->where('statut', self::STATUS_VALIDE);
    }

    /**
     * Scope for calculated pre-liquidations
     */
    public function scopeCalculated($query)
    {
        return $query->where('statut', self::STATUS_CALCULE);
    }

    /**
     * Mark as validated
     */
    public function markAsValidated(int $validatedBy): void
    {
        $this->update([
            'statut' => self::STATUS_VALIDE,
            'validated_by' => $validatedBy,
            'validated_at' => now(),
        ]);
    }

    /**
     * Generate next pre-liquidation number
     */
    public static function generateNextNumero(): string
    {
        $year = date('Y');
        $month = date('m');
        
        $lastPreLiquidation = static::where('numero_pre_liquidation', 'LIKE', "PLQ{$year}{$month}%")
                                   ->orderBy('numero_pre_liquidation', 'desc')
                                   ->first();

        if ($lastPreLiquidation) {
            $lastNumber = (int) substr($lastPreLiquidation->numero_pre_liquidation, -4);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }

        return "PLQ{$year}{$month}" . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }
}
