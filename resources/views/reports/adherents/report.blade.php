@extends('layouts.template')

@section('title', 'Rapport Adhérents - CRFM')

@section('page-header')
@section('page-title', 'Rapport Adhérents')
@section('page-description', 'Rapport généré le {{ $report['generated_at']->format('d/m/Y à H:i') }}')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ route('reports.index') }}">Rapports</a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Adhérents</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <!-- Statistiques du rapport -->
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-blue text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($report['statistics']['total']) }}</h4>
                        <h6 class="text-white m-b-0">Total</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-users f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-green text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($report['statistics']['actifs']) }}</h4>
                        <h6 class="text-white m-b-0">Actifs</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-check-circle f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-yellow text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($report['statistics']['nouveaux_mois']) }}</h4>
                        <h6 class="text-white m-b-0">Nouveaux ce mois</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-user-plus f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-purple text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($report['statistics']['age_moyen'], 1) }}</h4>
                        <h6 class="text-white m-b-0">Âge moyen</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-calendar f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@if(isset($report['by_employer']) && count($report['by_employer']) > 0)
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-building text-c-blue me-2"></i>Répartition par employeur</h5>
            </div>
            <div class="card-block">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Employeur</th>
                                <th class="text-end">Nombre</th>
                                <th class="text-end">%</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($report['by_employer'] as $employer)
                            <tr>
                                <td>{{ $employer->employeur ?: 'Non renseigné' }}</td>
                                <td class="text-end">{{ $employer->total }}</td>
                                <td class="text-end">{{ number_format(($employer->total / $report['statistics']['total']) * 100, 1) }}%</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-pie-chart text-c-green me-2"></i>Répartition par âge</h5>
            </div>
            <div class="card-block">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Tranche d'âge</th>
                                <th class="text-end">Nombre</th>
                                <th class="text-end">%</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if(isset($report['age_distribution']))
                                @foreach($report['age_distribution'] as $age_group => $count)
                                <tr>
                                    <td>{{ $age_group }}</td>
                                    <td class="text-end">{{ $count }}</td>
                                    <td class="text-end">{{ number_format(($count / $report['statistics']['total']) * 100, 1) }}%</td>
                                </tr>
                                @endforeach
                            @endif
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-list text-c-blue me-2"></i>Liste détaillée des adhérents</h5>
                <div class="card-header-right">
                    <span class="badge bg-info">{{ $report['adherents']->count() }} résultat(s)</span>
                </div>
            </div>
            <div class="card-block">
                @if($report['adherents']->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>N° Adhérent</th>
                                <th>Nom & Prénoms</th>
                                <th>Date naissance</th>
                                <th>Âge</th>
                                <th>Employeur</th>
                                <th>Statut</th>
                                <th>Date adhésion</th>
                                <th>Salaire base</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($report['adherents'] as $adherent)
                            <tr>
                                <td><strong>{{ $adherent->numero_adherent }}</strong></td>
                                <td>
                                    <div>
                                        <strong>{{ $adherent->full_name }}</strong>
                                        @if($adherent->email)
                                            <br><small class="text-muted">{{ $adherent->email }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>{{ $adherent->date_naissance ? $adherent->date_naissance->format('d/m/Y') : 'N/A' }}</td>
                                <td>{{ $adherent->age }} ans</td>
                                <td>{{ $adherent->employeur ?: 'N/A' }}</td>
                                <td>
                                    <span class="badge bg-{{ $adherent->status_color }}">
                                        {{ $adherent->status_label }}
                                    </span>
                                </td>
                                <td>{{ $adherent->date_adhesion ? $adherent->date_adhesion->format('d/m/Y') : 'N/A' }}</td>
                                <td>{{ $adherent->salaire_base ? number_format($adherent->salaire_base, 0, ',', ' ') . ' €' : 'N/A' }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="text-center py-4">
                    <i class="feather icon-users f-48 text-muted"></i>
                    <h5 class="mt-3">Aucun adhérent trouvé</h5>
                    <p class="text-muted">Aucun adhérent ne correspond aux critères sélectionnés.</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-download text-c-blue me-2"></i>Actions</h5>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-md-3">
                        <button class="btn btn-primary btn-block" onclick="exportPDF()">
                            <i class="feather icon-file-text me-2"></i>Exporter en PDF
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-success btn-block" onclick="exportExcel()">
                            <i class="feather icon-download me-2"></i>Exporter en Excel
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-info btn-block" onclick="imprimerRapport()">
                            <i class="feather icon-printer me-2"></i>Imprimer
                        </button>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ route('reports.adherents.form') }}" class="btn btn-outline-secondary btn-block">
                            <i class="feather icon-arrow-left me-2"></i>Nouveau rapport
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function exportPDF() {
    const params = new URLSearchParams(window.location.search);
    params.set('format', 'pdf');
    window.location.href = '{{ route("reports.adherents.generate") }}?' + params.toString();
}

function exportExcel() {
    const params = new URLSearchParams(window.location.search);
    params.set('format', 'excel');
    window.location.href = '{{ route("reports.adherents.generate") }}?' + params.toString();
}

function imprimerRapport() {
    window.print();
}
</script>
@endpush
