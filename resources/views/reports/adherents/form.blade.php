@extends('layouts.template')

@section('title', 'Rapport Adhérents - CRFM')

@section('page-header')
@section('page-title', 'Rapport Adhérents')
@section('page-description', 'Générer un rapport détaillé des adhérents')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ route('reports.index') }}">Rapports</a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Adhérents</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-users text-c-blue me-2"></i>Paramètres du rapport</h5>
            </div>
            <div class="card-block">
                <form action="{{ route('reports.adherents.generate') }}" method="POST" id="reportForm">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="statut">Statut</label>
                                <select class="form-control" id="statut" name="statut">
                                    <option value="">Tous les statuts</option>
                                    <option value="actif">Actif</option>
                                    <option value="suspendu">Suspendu</option>
                                    <option value="radie">Radié</option>
                                    <option value="retraite">Retraité</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="employeur">Employeur</label>
                                <input type="text" class="form-control" id="employeur" name="employeur" 
                                       placeholder="Nom de l'employeur (optionnel)">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_from">Date d'adhésion - Du</label>
                                <input type="date" class="form-control" id="date_from" name="date_from">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_to">Date d'adhésion - Au</label>
                                <input type="date" class="form-control" id="date_to" name="date_to">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="format">Format de sortie</label>
                                <select class="form-control" id="format" name="format">
                                    <option value="html">Affichage web</option>
                                    <option value="pdf">PDF</option>
                                    <option value="excel">Excel</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="tri">Trier par</label>
                                <select class="form-control" id="tri" name="tri">
                                    <option value="nom">Nom</option>
                                    <option value="date_adhesion">Date d'adhésion</option>
                                    <option value="employeur">Employeur</option>
                                    <option value="statut">Statut</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="inclure_statistiques" 
                                           name="inclure_statistiques" value="1" checked>
                                    <label class="form-check-label" for="inclure_statistiques">
                                        Inclure les statistiques détaillées
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group text-center">
                        <button type="button" class="btn btn-secondary me-2" onclick="previewReport()">
                            <i class="feather icon-eye me-2"></i>Aperçu
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="feather icon-download me-2"></i>Générer le rapport
                        </button>
                        <a href="{{ route('reports.index') }}" class="btn btn-outline-secondary ms-2">
                            <i class="feather icon-arrow-left me-2"></i>Retour
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal d'aperçu -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Aperçu du rapport</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary" onclick="generateFromPreview()">
                    Générer le rapport complet
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function previewReport() {
    const formData = new FormData(document.getElementById('reportForm'));
    formData.append('preview', '1');
    
    $('#previewModal').modal('show');
    
    fetch('{{ route("reports.api.preview") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('previewContent').innerHTML = generatePreviewHTML(data.data);
        } else {
            document.getElementById('previewContent').innerHTML = 
                '<div class="alert alert-danger">Erreur lors de la génération de l\'aperçu</div>';
        }
    })
    .catch(error => {
        document.getElementById('previewContent').innerHTML = 
            '<div class="alert alert-danger">Erreur de connexion</div>';
    });
}

function generatePreviewHTML(data) {
    let html = '<div class="table-responsive">';
    html += '<table class="table table-striped">';
    html += '<thead><tr><th>Nom</th><th>Employeur</th><th>Statut</th><th>Date adhésion</th></tr></thead>';
    html += '<tbody>';
    
    if (data.adherents && data.adherents.length > 0) {
        data.adherents.forEach(adherent => {
            html += `<tr>
                <td>${adherent.full_name}</td>
                <td>${adherent.employeur || 'N/A'}</td>
                <td><span class="badge bg-${getStatusColor(adherent.statut)}">${adherent.status_label}</span></td>
                <td>${formatDate(adherent.date_adhesion)}</td>
            </tr>`;
        });
    } else {
        html += '<tr><td colspan="4" class="text-center">Aucun résultat trouvé</td></tr>';
    }
    
    html += '</tbody></table></div>';
    
    if (data.statistics) {
        html += '<div class="mt-3"><h6>Statistiques</h6>';
        html += `<p>Total: ${data.statistics.total} adhérents</p>`;
        html += '</div>';
    }
    
    return html;
}

function getStatusColor(status) {
    const colors = {
        'actif': 'success',
        'suspendu': 'warning',
        'radie': 'danger',
        'retraite': 'info'
    };
    return colors[status] || 'secondary';
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('fr-FR');
}

function generateFromPreview() {
    $('#previewModal').modal('hide');
    document.getElementById('reportForm').submit();
}

// Validation du formulaire
document.getElementById('reportForm').addEventListener('submit', function(e) {
    const dateFrom = document.getElementById('date_from').value;
    const dateTo = document.getElementById('date_to').value;
    
    if (dateFrom && dateTo && dateFrom > dateTo) {
        e.preventDefault();
        alert('La date de début doit être antérieure à la date de fin');
        return false;
    }
});
</script>
@endpush
