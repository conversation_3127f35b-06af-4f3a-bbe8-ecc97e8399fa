@extends('layouts.template')

@section('title', 'Rôles et Permissions - CRFM')

@section('page-header')
@section('page-title', 'Rôles et Permissions')
@section('page-description', 'Gestion des rôles utilisateurs et de leurs permissions')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ route('parametres.index') }}">Paramètres</a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Rôles et Permissions</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <!-- Vue d'ensemble des rôles -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-shield text-c-blue me-2"></i>Vue d'ensemble des rôles</h5>
            </div>
            <div class="card-block">
                <div class="row">
                    @foreach($roles as $roleKey => $role)
                    <div class="col-md-4">
                        <div class="role-card">
                            <div class="role-header">
                                <span class="badge bg-{{ $role['color'] }} fs-6">{{ $role['name'] }}</span>
                                <span class="text-muted ms-2">{{ $role['users_count'] }} utilisateur(s)</span>
                            </div>
                            <p class="text-muted mt-2">{{ $role['description'] }}</p>
                            <div class="permissions-count">
                                <small class="text-info">{{ count($role['permissions']) }} permission(s)</small>
                            </div>
                            <button class="btn btn-outline-primary btn-sm mt-2" onclick="showRoleDetails('{{ $roleKey }}')">
                                <i class="feather icon-eye me-1"></i>Voir détails
                            </button>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Matrice des permissions -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-grid text-c-green me-2"></i>Matrice des permissions</h5>
                <div class="card-header-right">
                    <button class="btn btn-primary btn-sm" onclick="editPermissions()">
                        <i class="feather icon-edit me-1"></i>Modifier les permissions
                    </button>
                </div>
            </div>
            <div class="card-block">
                <div class="table-responsive">
                    <table class="table table-hover permissions-matrix">
                        <thead>
                            <tr>
                                <th>Permission</th>
                                <th>Description</th>
                                <th class="text-center">
                                    <span class="badge bg-danger">Administrateur</span>
                                </th>
                                <th class="text-center">
                                    <span class="badge bg-warning">Gestionnaire</span>
                                </th>
                                <th class="text-center">
                                    <span class="badge bg-info">Opérateur</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @php
                                $groupedPermissions = collect($permissions)->groupBy('category');
                            @endphp
                            
                            @foreach($groupedPermissions as $category => $categoryPermissions)
                            <tr class="category-header">
                                <td colspan="5">
                                    <strong class="text-primary">{{ $category }}</strong>
                                </td>
                            </tr>
                            @foreach($categoryPermissions as $permissionKey => $permission)
                            <tr>
                                <td>
                                    <strong>{{ $permission['name'] }}</strong>
                                </td>
                                <td>
                                    <small class="text-muted">{{ $permission['description'] }}</small>
                                </td>
                                <td class="text-center">
                                    @if(in_array($permissionKey, $roles['admin']['permissions']))
                                        <i class="feather icon-check text-success"></i>
                                    @else
                                        <i class="feather icon-x text-muted"></i>
                                    @endif
                                </td>
                                <td class="text-center">
                                    @if(in_array($permissionKey, $roles['gestionnaire']['permissions']))
                                        <i class="feather icon-check text-success"></i>
                                    @else
                                        <i class="feather icon-x text-muted"></i>
                                    @endif
                                </td>
                                <td class="text-center">
                                    @if(in_array($permissionKey, $roles['operateur']['permissions']))
                                        <i class="feather icon-check text-success"></i>
                                    @else
                                        <i class="feather icon-x text-muted"></i>
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de détails du rôle -->
<div class="modal fade" id="roleDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Détails du rôle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="roleDetailsContent">
                <!-- Contenu dynamique -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal d'édition des permissions -->
<div class="modal fade" id="editPermissionsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Modifier les permissions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('roles.update-permissions') }}">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="feather icon-info me-2"></i>
                        <strong>Note :</strong> Cette fonctionnalité est en cours de développement. 
                        Les permissions sont actuellement définies dans le code.
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <h6>Sélectionner un rôle</h6>
                            <select name="role" class="form-control" id="roleSelect">
                                <option value="">Choisir un rôle</option>
                                <option value="admin">Administrateur</option>
                                <option value="gestionnaire">Gestionnaire</option>
                                <option value="operateur">Opérateur</option>
                            </select>
                        </div>
                        <div class="col-md-8">
                            <h6>Permissions disponibles</h6>
                            <div id="permissionsCheckboxes">
                                <!-- Contenu dynamique -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Enregistrer</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.role-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
}
.role-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.role-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}
.permissions-matrix th {
    background-color: #f8f9fa;
    font-weight: 600;
}
.category-header td {
    background-color: #f1f3f4;
    font-weight: 600;
    border-top: 2px solid #dee2e6;
}
.permissions-count {
    margin-top: 0.5rem;
}
.card-block {
    padding: 1.5rem;
}
</style>
@endpush

@push('scripts')
<script>
const rolesData = @json($roles);
const permissionsData = @json($permissions);

function showRoleDetails(roleKey) {
    const role = rolesData[roleKey];
    if (!role) return;
    
    let content = `
        <div class="row">
            <div class="col-md-6">
                <h6>Informations générales</h6>
                <ul class="list-unstyled">
                    <li><strong>Nom :</strong> ${role.name}</li>
                    <li><strong>Description :</strong> ${role.description}</li>
                    <li><strong>Utilisateurs :</strong> ${role.users_count}</li>
                    <li><strong>Permissions :</strong> ${role.permissions.length}</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>Permissions accordées</h6>
                <ul class="list-unstyled">
    `;
    
    role.permissions.forEach(permission => {
        const permissionData = permissionsData[permission];
        if (permissionData) {
            content += `<li><i class="feather icon-check text-success me-2"></i>${permissionData.name}</li>`;
        }
    });
    
    content += `
                </ul>
            </div>
        </div>
    `;
    
    document.getElementById('roleDetailsContent').innerHTML = content;
    const modal = new bootstrap.Modal(document.getElementById('roleDetailsModal'));
    modal.show();
}

function editPermissions() {
    const modal = new bootstrap.Modal(document.getElementById('editPermissionsModal'));
    modal.show();
}

// Gestion du changement de rôle dans le modal d'édition
document.addEventListener('DOMContentLoaded', function() {
    const roleSelect = document.getElementById('roleSelect');
    const permissionsContainer = document.getElementById('permissionsCheckboxes');
    
    if (roleSelect) {
        roleSelect.addEventListener('change', function() {
            const selectedRole = this.value;
            if (!selectedRole) {
                permissionsContainer.innerHTML = '';
                return;
            }
            
            const rolePermissions = rolesData[selectedRole].permissions;
            let content = '';
            
            // Grouper les permissions par catégorie
            const groupedPermissions = {};
            Object.keys(permissionsData).forEach(key => {
                const category = permissionsData[key].category;
                if (!groupedPermissions[category]) {
                    groupedPermissions[category] = [];
                }
                groupedPermissions[category].push({key, ...permissionsData[key]});
            });
            
            Object.keys(groupedPermissions).forEach(category => {
                content += `<h6 class="mt-3">${category}</h6>`;
                groupedPermissions[category].forEach(permission => {
                    const isChecked = rolePermissions.includes(permission.key) ? 'checked' : '';
                    content += `
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="permissions[]" 
                                   value="${permission.key}" id="perm_${permission.key}" ${isChecked}>
                            <label class="form-check-label" for="perm_${permission.key}">
                                <strong>${permission.name}</strong><br>
                                <small class="text-muted">${permission.description}</small>
                            </label>
                        </div>
                    `;
                });
            });
            
            permissionsContainer.innerHTML = content;
        });
    }
});
</script>
@endpush
