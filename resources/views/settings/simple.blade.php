@extends('layouts.template')

@section('title', 'Paramètres Système - CRFM')

@section('page-header')
@section('page-title', 'Paramètres Système')
@section('page-description', 'Configuration générale du système')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Paramètres</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Paramètres Système</h5>
            </div>
            <div class="card-block">
                <form method="POST" action="{{ route('settings.update') }}">
                    @csrf
                    @method('PUT')
                    
                    <div class="form-group">
                        <label>Nom de l'application</label>
                        <input type="text" name="app_name" class="form-control" 
                               value="{{ $settings['app_name'] }}" required>
                    </div>
                    
                    <div class="form-group">
                        <label>Description</label>
                        <textarea name="app_description" class="form-control" rows="3">{{ $settings['app_description'] }}</textarea>
                    </div>
                    
                    <div class="form-group">
                        <label>Nom de l'organisation</label>
                        <input type="text" name="organization_name" class="form-control" 
                               value="{{ $settings['organization_name'] }}" required>
                    </div>
                    
                    <div class="form-group">
                        <label>Fuseau horaire</label>
                        <select name="timezone" class="form-control" required>
                            <option value="Africa/Bamako" {{ $settings['timezone'] == 'Africa/Bamako' ? 'selected' : '' }}>Africa/Bamako</option>
                            <option value="UTC" {{ $settings['timezone'] == 'UTC' ? 'selected' : '' }}>UTC</option>
                            <option value="Europe/Paris" {{ $settings['timezone'] == 'Europe/Paris' ? 'selected' : '' }}>Europe/Paris</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>Format de date</label>
                        <select name="date_format" class="form-control" required>
                            <option value="d/m/Y" {{ $settings['date_format'] == 'd/m/Y' ? 'selected' : '' }}>DD/MM/YYYY</option>
                            <option value="m/d/Y" {{ $settings['date_format'] == 'm/d/Y' ? 'selected' : '' }}>MM/DD/YYYY</option>
                            <option value="Y-m-d" {{ $settings['date_format'] == 'Y-m-d' ? 'selected' : '' }}>YYYY-MM-DD</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>Devise</label>
                        <select name="currency" class="form-control" required>
                            <option value="FCFA" {{ $settings['currency'] == 'FCFA' ? 'selected' : '' }}>FCFA</option>
                            <option value="EUR" {{ $settings['currency'] == 'EUR' ? 'selected' : '' }}>EUR</option>
                            <option value="USD" {{ $settings['currency'] == 'USD' ? 'selected' : '' }}>USD</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" name="maintenance_mode" class="form-check-input" id="maintenance_mode" 
                                   value="1" {{ $settings['maintenance_mode'] ? 'checked' : '' }}>
                            <label class="form-check-label" for="maintenance_mode">
                                Mode maintenance
                            </label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="feather icon-save me-2"></i>Enregistrer
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.form-group {
    margin-bottom: 1rem;
}
.card-block {
    padding: 1.5rem;
}
</style>
@endpush
