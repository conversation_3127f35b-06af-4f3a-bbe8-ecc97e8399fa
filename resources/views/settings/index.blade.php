@extends('layouts.template')

@section('title', 'Paramètres Système - CRFM')

@section('page-header')
@section('page-title', 'Paramètres Système')
@section('page-description', 'Configuration générale du système')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Paramètres</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <!-- Navigation des onglets -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="settingsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                            <i class="feather icon-settings me-2"></i>Général
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="organization-tab" data-bs-toggle="tab" data-bs-target="#organization" type="button" role="tab">
                            <i class="feather icon-building me-2"></i>Organisation
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="appearance-tab" data-bs-toggle="tab" data-bs-target="#appearance" type="button" role="tab">
                            <i class="feather icon-image me-2"></i>Apparence
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                            <i class="feather icon-server me-2"></i>Système
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications" type="button" role="tab">
                            <i class="feather icon-bell me-2"></i>Notifications
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<form method="POST" action="{{ route('settings.update') }}" enctype="multipart/form-data" class="needs-validation" novalidate>
    @csrf
    @method('PUT')
    
    <div class="tab-content" id="settingsTabContent">
        <!-- Onglet Général -->
        <div class="tab-pane fade show active" id="general" role="tabpanel">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="feather icon-settings text-c-blue me-2"></i>Paramètres généraux</h5>
                        </div>
                        <div class="card-block">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="required">Nom de l'application</label>
                                        <input type="text" name="app_name" class="form-control @error('app_name') is-invalid @enderror" 
                                               value="{{ old('app_name', $settings['app_name']) }}" required>
                                        @error('app_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="required">Fuseau horaire</label>
                                        <select name="timezone" class="form-control @error('timezone') is-invalid @enderror" required>
                                            <option value="Africa/Bamako" {{ $settings['timezone'] == 'Africa/Bamako' ? 'selected' : '' }}>Africa/Bamako</option>
                                            <option value="UTC" {{ $settings['timezone'] == 'UTC' ? 'selected' : '' }}>UTC</option>
                                            <option value="Europe/Paris" {{ $settings['timezone'] == 'Europe/Paris' ? 'selected' : '' }}>Europe/Paris</option>
                                        </select>
                                        @error('timezone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>Description de l'application</label>
                                <textarea name="app_description" class="form-control @error('app_description') is-invalid @enderror" 
                                          rows="3">{{ old('app_description', $settings['app_description']) }}</textarea>
                                @error('app_description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="required">Format de date</label>
                                        <select name="date_format" class="form-control @error('date_format') is-invalid @enderror" required>
                                            <option value="d/m/Y" {{ $settings['date_format'] == 'd/m/Y' ? 'selected' : '' }}>DD/MM/YYYY</option>
                                            <option value="m/d/Y" {{ $settings['date_format'] == 'm/d/Y' ? 'selected' : '' }}>MM/DD/YYYY</option>
                                            <option value="Y-m-d" {{ $settings['date_format'] == 'Y-m-d' ? 'selected' : '' }}>YYYY-MM-DD</option>
                                        </select>
                                        @error('date_format')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="required">Devise</label>
                                        <select name="currency" class="form-control @error('currency') is-invalid @enderror" required>
                                            <option value="FCFA" {{ $settings['currency'] == 'FCFA' ? 'selected' : '' }}>FCFA</option>
                                            <option value="EUR" {{ $settings['currency'] == 'EUR' ? 'selected' : '' }}>EUR</option>
                                            <option value="USD" {{ $settings['currency'] == 'USD' ? 'selected' : '' }}>USD</option>
                                        </select>
                                        @error('currency')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="required">Langue</label>
                                        <select name="language" class="form-control @error('language') is-invalid @enderror" required>
                                            <option value="fr" {{ $settings['language'] == 'fr' ? 'selected' : '' }}>Français</option>
                                            <option value="en" {{ $settings['language'] == 'en' ? 'selected' : '' }}>English</option>
                                        </select>
                                        @error('language')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="feather icon-sliders text-c-green me-2"></i>Préférences</h5>
                        </div>
                        <div class="card-block">
                            <div class="form-group">
                                <label class="required">Éléments par page</label>
                                <select name="items_per_page" class="form-control @error('items_per_page') is-invalid @enderror" required>
                                    <option value="10" {{ $settings['items_per_page'] == 10 ? 'selected' : '' }}>10</option>
                                    <option value="15" {{ $settings['items_per_page'] == 15 ? 'selected' : '' }}>15</option>
                                    <option value="25" {{ $settings['items_per_page'] == 25 ? 'selected' : '' }}>25</option>
                                    <option value="50" {{ $settings['items_per_page'] == 50 ? 'selected' : '' }}>50</option>
                                </select>
                                @error('items_per_page')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="form-group">
                                <label class="required">Timeout de session (minutes)</label>
                                <input type="number" name="session_timeout" class="form-control @error('session_timeout') is-invalid @enderror" 
                                       value="{{ old('session_timeout', $settings['session_timeout']) }}" min="15" max="1440" required>
                                @error('session_timeout')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" name="registration_enabled" class="form-check-input" id="registration_enabled" 
                                           value="1" {{ $settings['registration_enabled'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="registration_enabled">
                                        Permettre l'inscription
                                    </label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" name="maintenance_mode" class="form-check-input" id="maintenance_mode" 
                                           value="1" {{ $settings['maintenance_mode'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="maintenance_mode">
                                        Mode maintenance
                                    </label>
                                </div>
                                <small class="form-text text-muted">Seuls les administrateurs pourront accéder au système</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Organisation -->
        <div class="tab-pane fade" id="organization" role="tabpanel">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="feather icon-building text-c-blue me-2"></i>Informations de l'organisation</h5>
                        </div>
                        <div class="card-block">
                            <div class="form-group">
                                <label class="required">Nom de l'organisation</label>
                                <input type="text" name="organization_name" class="form-control @error('organization_name') is-invalid @enderror" 
                                       value="{{ old('organization_name', $settings['organization_name']) }}" required>
                                @error('organization_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="form-group">
                                <label>Adresse</label>
                                <textarea name="organization_address" class="form-control @error('organization_address') is-invalid @enderror" 
                                          rows="3">{{ old('organization_address', $settings['organization_address']) }}</textarea>
                                @error('organization_address')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Téléphone</label>
                                        <input type="text" name="organization_phone" class="form-control @error('organization_phone') is-invalid @enderror" 
                                               value="{{ old('organization_phone', $settings['organization_phone']) }}">
                                        @error('organization_phone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Email</label>
                                        <input type="email" name="organization_email" class="form-control @error('organization_email') is-invalid @enderror" 
                                               value="{{ old('organization_email', $settings['organization_email']) }}">
                                        @error('organization_email')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>Site web</label>
                                <input type="url" name="organization_website" class="form-control @error('organization_website') is-invalid @enderror" 
                                       value="{{ old('organization_website', $settings['organization_website']) }}">
                                @error('organization_website')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Apparence -->
        <div class="tab-pane fade" id="appearance" role="tabpanel">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="feather icon-image text-c-blue me-2"></i>Apparence et branding</h5>
                        </div>
                        <div class="card-block">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Logo de l'application</label>
                                        <input type="file" name="logo" class="form-control @error('logo') is-invalid @enderror"
                                               accept="image/jpeg,image/png,image/jpg,image/gif">
                                        <small class="form-text text-muted">Formats acceptés: JPEG, PNG, JPG, GIF. Taille max: 2MB</small>
                                        @error('logo')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        @if($settings['logo'])
                                        <div class="mt-2">
                                            <img src="{{ Storage::url($settings['logo']) }}" alt="Logo actuel" class="img-thumbnail" style="max-height: 100px;">
                                        </div>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Favicon</label>
                                        <input type="file" name="favicon" class="form-control @error('favicon') is-invalid @enderror"
                                               accept="image/x-icon,image/png">
                                        <small class="form-text text-muted">Formats acceptés: ICO, PNG. Taille max: 512KB</small>
                                        @error('favicon')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        @if($settings['favicon'])
                                        <div class="mt-2">
                                            <img src="{{ Storage::url($settings['favicon']) }}" alt="Favicon actuel" class="img-thumbnail" style="max-height: 32px;">
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Système -->
        <div class="tab-pane fade" id="system" role="tabpanel">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="feather icon-server text-c-blue me-2"></i>Configuration système</h5>
                        </div>
                        <div class="card-block">
                            <div class="form-group">
                                <label class="required">Fréquence de sauvegarde</label>
                                <select name="backup_frequency" class="form-control @error('backup_frequency') is-invalid @enderror" required>
                                    <option value="daily" {{ $settings['backup_frequency'] == 'daily' ? 'selected' : '' }}>Quotidienne</option>
                                    <option value="weekly" {{ $settings['backup_frequency'] == 'weekly' ? 'selected' : '' }}>Hebdomadaire</option>
                                    <option value="monthly" {{ $settings['backup_frequency'] == 'monthly' ? 'selected' : '' }}>Mensuelle</option>
                                </select>
                                @error('backup_frequency')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <h6 class="text-muted mb-3">Actions système</h6>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-warning" onclick="clearCache()">
                                            <i class="feather icon-trash-2 me-2"></i>Vider le cache
                                        </button>
                                        <button type="button" class="btn btn-info" onclick="exportSettings()">
                                            <i class="feather icon-download me-2"></i>Exporter paramètres
                                        </button>
                                        <button type="button" class="btn btn-secondary" onclick="importSettings()">
                                            <i class="feather icon-upload me-2"></i>Importer paramètres
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="feather icon-info text-c-green me-2"></i>Informations système</h5>
                        </div>
                        <div class="card-block">
                            <div class="info-item">
                                <strong>Version PHP :</strong><br>
                                <small class="text-muted">{{ PHP_VERSION }}</small>
                            </div>
                            <div class="info-item">
                                <strong>Version Laravel :</strong><br>
                                <small class="text-muted">{{ app()->version() }}</small>
                            </div>
                            <div class="info-item">
                                <strong>Environnement :</strong><br>
                                <span class="badge bg-{{ app()->environment() === 'production' ? 'success' : 'warning' }}">
                                    {{ ucfirst(app()->environment()) }}
                                </span>
                            </div>
                            <div class="info-item">
                                <strong>Mode debug :</strong><br>
                                <span class="badge bg-{{ config('app.debug') ? 'danger' : 'success' }}">
                                    {{ config('app.debug') ? 'Activé' : 'Désactivé' }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Notifications -->
        <div class="tab-pane fade" id="notifications" role="tabpanel">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="feather icon-bell text-c-blue me-2"></i>Configuration des notifications</h5>
                        </div>
                        <div class="card-block">
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" name="email_notifications" class="form-check-input" id="email_notifications"
                                           value="1" {{ $settings['email_notifications'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="email_notifications">
                                        Notifications par email
                                    </label>
                                </div>
                                <small class="form-text text-muted">Envoyer des notifications par email aux utilisateurs</small>
                            </div>

                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" name="sms_notifications" class="form-check-input" id="sms_notifications"
                                           value="1" {{ $settings['sms_notifications'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="sms_notifications">
                                        Notifications par SMS
                                    </label>
                                </div>
                                <small class="form-text text-muted">Envoyer des notifications par SMS (nécessite une configuration supplémentaire)</small>
                            </div>

                            <div class="form-group">
                                <label>Tester la configuration email</label>
                                <div class="input-group">
                                    <input type="email" id="test_email" class="form-control" placeholder="<EMAIL>">
                                    <button type="button" class="btn btn-info" onclick="testEmail()">
                                        <i class="feather icon-send me-2"></i>Tester
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Boutons d'action -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-block text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="feather icon-save me-2"></i>Enregistrer les paramètres
                    </button>
                    <button type="button" class="btn btn-secondary btn-lg ms-2" onclick="resetForm()">
                        <i class="feather icon-refresh-cw me-2"></i>Réinitialiser
                    </button>
                    <button type="button" class="btn btn-info btn-lg ms-2" onclick="exportSettings()">
                        <i class="feather icon-download me-2"></i>Exporter
                    </button>
                    <button type="button" class="btn btn-warning btn-lg ms-2" onclick="clearCache()">
                        <i class="feather icon-trash-2 me-2"></i>Vider le cache
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Modal d'import des paramètres -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Importer des paramètres</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('settings.import') }}" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label class="required">Fichier de paramètres</label>
                        <input type="file" name="settings_file" class="form-control" accept=".json" required>
                        <small class="form-text text-muted">Sélectionnez un fichier JSON contenant les paramètres</small>
                    </div>
                    <div class="alert alert-warning">
                        <i class="feather icon-alert-triangle me-2"></i>
                        Cette action remplacera tous les paramètres actuels !
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Importer</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.required::after {
    content: " *";
    color: red;
}
.form-group {
    margin-bottom: 1rem;
}
.card-block {
    padding: 1.5rem;
}
.info-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #eee;
}
.info-item:last-child {
    border-bottom: none;
}
.nav-tabs .nav-link {
    border: none;
    border-bottom: 2px solid transparent;
}
.nav-tabs .nav-link.active {
    border-bottom-color: #007bff;
    background-color: transparent;
}
.tab-content {
    padding-top: 1rem;
}
</style>
@endpush

@push('scripts')
<script>
// Validation du formulaire
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

function resetForm() {
    if (confirm('Réinitialiser tous les champs ?')) {
        document.querySelector('form').reset();
    }
}

function clearCache() {
    if (confirm('Vider le cache du système ?')) {
        fetch('{{ route("settings.clear-cache") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                CRFM.showToast('Cache vidé avec succès', 'success');
            } else {
                CRFM.showToast('Erreur lors du vidage du cache', 'error');
            }
        })
        .catch(error => {
            CRFM.showToast('Erreur lors du vidage du cache', 'error');
        });
    }
}

function exportSettings() {
    window.location.href = '{{ route("settings.export") }}';
}

function importSettings() {
    const modal = new bootstrap.Modal(document.getElementById('importModal'));
    modal.show();
}

function testEmail() {
    const email = document.getElementById('test_email').value;
    if (!email) {
        CRFM.showToast('Veuillez saisir une adresse email', 'warning');
        return;
    }

    fetch('{{ route("settings.test-email") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ test_email: email })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            CRFM.showToast('Email de test envoyé avec succès', 'success');
        } else {
            CRFM.showToast('Erreur lors de l\'envoi de l\'email', 'error');
        }
    })
    .catch(error => {
        CRFM.showToast('Erreur lors de l\'envoi de l\'email', 'error');
    });
}

// Sauvegarde automatique des onglets
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('#settingsTabs button[data-bs-toggle="tab"]');

    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function(e) {
            localStorage.setItem('activeSettingsTab', e.target.id);
        });
    });

    // Restaurer l'onglet actif
    const activeTab = localStorage.getItem('activeSettingsTab');
    if (activeTab) {
        const tabButton = document.getElementById(activeTab);
        if (tabButton) {
            const tab = new bootstrap.Tab(tabButton);
            tab.show();
        }
    }
});
</script>
@endpush
