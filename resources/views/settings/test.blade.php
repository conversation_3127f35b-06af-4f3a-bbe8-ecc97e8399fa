@extends('layouts.template')

@section('title', 'Test Paramètres - CRFM')

@section('page-header')
@section('page-title', 'Test Paramètres')
@section('page-description', 'Test de la configuration')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Test</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Test des Paramètres</h5>
            </div>
            <div class="card-block">
                <form method="POST" action="{{ route('settings.update') }}">
                    @csrf
                    @method('PUT')
                    
                    <div class="form-group">
                        <label>Nom de l'application</label>
                        <input type="text" name="app_name" class="form-control" 
                               value="{{ $settings['app_name'] ?? 'CRFM' }}" required>
                    </div>
                    
                    <div class="form-group">
                        <label>Description</label>
                        <textarea name="app_description" class="form-control" rows="3">{{ $settings['app_description'] ?? '' }}</textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        Enregistrer
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.form-group {
    margin-bottom: 1rem;
}
.card-block {
    padding: 1.5rem;
}
</style>
@endpush
