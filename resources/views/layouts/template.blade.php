@php
    // S'assurer que $errors est toujours disponible
    $errors = $errors ?? new \Illuminate\Support\MessageBag();
@endphp
<!DOCTYPE html>
<html lang="fr">

<head>
    <title>@yield('title', 'CRFM - Gestion des cotisations et pré-liquidation de pension')</title>
    <!-- Meta -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="description" content="Application de gestion des cotisations et de pré-liquidation de pension pour le CRFM">
    <meta name="keywords" content="CRFM, cotisations, pension, pré-liquidation, organisme public">
    <meta name="author" content="CRFM">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!-- Favicon icon -->
    <link rel="icon" href="{{ asset('files/assets/images/favicon.ico') }}" type="image/x-icon">
    <!-- Google font-->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:400,600" rel="stylesheet">
    <!-- Required Fremwork -->
    <link rel="stylesheet" type="text/css" href="{{ asset('files/bower_components/bootstrap/dist/css/bootstrap.min.css') }}">
    <!-- feather Awesome -->
    <link rel="stylesheet" type="text/css" href="{{ asset('files/assets/icon/feather/css/feather.css') }}">
    <!-- Font Awesome -->
    <link rel="stylesheet" type="text/css" href="{{ asset('files/assets/icon/font-awesome/css/font-awesome.min.css') }}">
    <!-- Style.css -->
    <link rel="stylesheet" type="text/css" href="{{ asset('files/assets/css/style.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('files/assets/css/jquery.mCustomScrollbar.css') }}">

    <!-- Chart.js pour les statistiques -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    @stack('styles')
</head>

<body>
    <!-- Pre-loader start -->
    <div class="theme-loader">
        <div class="ball-scale">
            <div class='contain'>
                <div class="ring"><div class="frame"></div></div>
                <div class="ring"><div class="frame"></div></div>
                <div class="ring"><div class="frame"></div></div>
                <div class="ring"><div class="frame"></div></div>
                <div class="ring"><div class="frame"></div></div>
                <div class="ring"><div class="frame"></div></div>
                <div class="ring"><div class="frame"></div></div>
                <div class="ring"><div class="frame"></div></div>
                <div class="ring"><div class="frame"></div></div>
                <div class="ring"><div class="frame"></div></div>
            </div>
        </div>
    </div>
    <!-- Pre-loader end -->

    <div id="pcoded" class="pcoded">
        <div class="pcoded-overlay-box"></div>
        <div class="pcoded-container navbar-wrapper">
            <nav class="navbar header-navbar pcoded-header">
                <div class="navbar-wrapper">
                    <div class="navbar-logo">
                        <a class="mobile-menu" id="mobile-collapse" href="#!">
                            <i class="feather icon-menu"></i>
                        </a>
                        <a href="{{ route('dashboard.index') }}">
                            <h4 class="text-center py-2 m-0">CRFM</h4>
                        </a>
                        <a class="mobile-options">
                            <i class="feather icon-more-horizontal"></i>
                        </a>
                    </div>
                    <div class="navbar-container container-fluid">
                        <ul class="nav-right">
                            <li class="user-profile header-notification">
                                <div class="dropdown-primary dropdown">
                                    <div class="dropdown-toggle" data-bs-toggle="dropdown">
                                        <img src="{{ asset('files/assets/images/avatar-4.jpg') }}" class="img-radius" alt="User-Profile-Image">
                                        <span>{{ auth()->user()->name }}</span>
                                        <i class="feather icon-chevron-down"></i>
                                    </div>
                                    <ul class="show-notification profile-notification dropdown-menu" data-dropdown-in="fadeIn" data-dropdown-out="fadeOut">
                                        <li>
                                            <a href="#!">
                                                <i class="feather icon-settings"></i> Paramètres
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#!">
                                                <i class="feather icon-user"></i> Profil
                                            </a>
                                        </li>
                                        <li>
                                            <form method="POST" action="{{ route('auth.logout') }}">
                                                @csrf
                                                <button type="submit" style="background: none; border: none; width: 100%; text-align: left; padding: 10px 20px; color: inherit;">
                                                    <i class="feather icon-log-out"></i> Quitter
                                                </button>
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>

            <!-- Menu sidebar -->
            <div class="pcoded-main-container">
                <div class="pcoded-wrapper">
                    <nav class="pcoded-navbar">
                        <div class="pcoded-inner-navbar main-menu">
                            <div class="pcoded-navigatio-lavel">Menu principal</div>
                            <ul class="pcoded-item pcoded-left-item">
                                <li class="{{ request()->routeIs('dashboard.index') ? 'active' : '' }}">
                                    <a href="{{ route('dashboard.index') }}">
                                        <span class="pcoded-micon"><i class="feather icon-home"></i></span>
                                        <span class="pcoded-mtext">Tableau de bord</span>
                                    </a>
                                </li>
                                <li class="pcoded-hasmenu {{ request()->routeIs('affiliation.*', 'declaration.*', 'pre-liquidation.*') ? 'pcoded-trigger' : '' }}">
                                    <a href="javascript:void(0)">
                                        <span class="pcoded-micon"><i class="feather icon-users"></i></span>
                                        <span class="pcoded-mtext">Dossiers</span>
                                    </a>
                                    <ul class="pcoded-submenu">
                                        <li class="{{ request()->routeIs('affiliation.*') ? 'active' : '' }}">
                                            <a href="{{ route('affiliation.index') }}">
                                                <span class="pcoded-mtext">Affiliation</span>
                                            </a>
                                        </li>
                                        <li class="{{ request()->routeIs('declaration.*') ? 'active' : '' }}">
                                            <a href="{{ route('declaration.index') }}">
                                                <span class="pcoded-mtext">Déclaration</span>
                                            </a>
                                        </li>
                                        <li class="{{ request()->routeIs('pre-liquidation.*') ? 'active' : '' }}">
                                            <a href="{{ route('pre-liquidation.index') }}">
                                                <span class="pcoded-mtext">Pré-liquidation</span>
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                                <li class="{{ request()->routeIs('statistiques.*') ? 'active' : '' }}">
                                    <a href="{{ route('statistiques.index') }}">
                                        <span class="pcoded-micon"><i class="feather icon-bar-chart"></i></span>
                                        <span class="pcoded-mtext">Statistiques</span>
                                    </a>
                                </li>
                                <li class="pcoded-hasmenu {{ request()->routeIs('settings.*', 'users.*') ? 'pcoded-trigger' : '' }}">
                                    <a href="javascript:void(0)">
                                        <span class="pcoded-micon"><i class="feather icon-settings"></i></span>
                                        <span class="pcoded-mtext">Paramètres</span>
                                    </a>
                                    <ul class="pcoded-submenu">
                                        <li class="{{ request()->routeIs('settings.*') ? 'active' : '' }}">
                                            <a href="{{ route('settings.index') }}">
                                                <span class="pcoded-mtext">Configuration système</span>
                                            </a>
                                        </li>
                                        <li class="{{ request()->routeIs('users.*') ? 'active' : '' }}">
                                            <a href="{{ route('users.index') }}">
                                                <span class="pcoded-mtext">Gestion des utilisateurs</span>
                                            </a>
                                        </li>
                                        <li class="{{ request()->routeIs('roles.*') ? 'active' : '' }}">
                                            <a href="{{ route('roles.index') }}">
                                                <span class="pcoded-mtext">Rôles et permissions</span>
                                            </a>
                                        </li>
                                        <li class="{{ request()->routeIs('security.*') ? 'active' : '' }}">
                                            <a href="{{ route('security.index') }}">
                                                <span class="pcoded-mtext">Sécurité</span>
                                            </a>
                                        </li>
                                        <li class="{{ request()->routeIs('notifications.*') ? 'active' : '' }}">
                                            <a href="{{ route('notifications.index') }}">
                                                <span class="pcoded-mtext">Notifications</span>
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                                <li class="{{ request()->routeIs('outils.*') ? 'active' : '' }}">
                                    <a href="{{ route('outils.index') }}">
                                        <span class="pcoded-micon"><i class="feather icon-tool"></i></span>
                                        <span class="pcoded-mtext">Outils</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </nav>
                    <div class="pcoded-content">
                        <div class="pcoded-inner-content">
                            <div class="main-body">
                                <div class="page-wrapper">
                                    <!-- Page-header start -->
                                    @hasSection('page-header')
                                    <div class="page-header">
                                        <div class="row align-items-end">
                                            <div class="col-lg-8">
                                                <div class="page-header-title">
                                                    <div class="d-inline">
                                                        <h4>@yield('page-title')</h4>
                                                        <span>@yield('page-description')</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-4">
                                                <div class="page-header-breadcrumb">
                                                    @yield('breadcrumb')
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @endif
                                    <!-- Page-header end -->
                                    
                                    <div class="page-body">
                                        <!-- Flash Messages -->
                                        @if (session('success'))
                                            <div class="alert alert-success alert-dismissible fade show">
                                                <i class="feather icon-check-circle me-2"></i>{{ session('success') }}
                                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                            </div>
                                        @endif
                                        
                                        @if (session('error'))
                                            <div class="alert alert-danger alert-dismissible fade show">
                                                <i class="feather icon-alert-circle me-2"></i>{{ session('error') }}
                                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                            </div>
                                        @endif
                                        
                                        @if (session('warning'))
                                            <div class="alert alert-warning alert-dismissible fade show">
                                                <i class="feather icon-alert-triangle me-2"></i>{{ session('warning') }}
                                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                            </div>
                                        @endif
                                        
                                        @if ($errors->any())
                                            <div class="alert alert-danger alert-dismissible fade show">
                                                <i class="feather icon-alert-circle me-2"></i>
                                                <ul class="mb-0">
                                                    @foreach ($errors->all() as $error)
                                                        <li>{{ $error }}</li>
                                                    @endforeach
                                                </ul>
                                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                            </div>
                                        @endif
                                        
                                        @yield('content')
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Required Js -->
    <script type="text/javascript" src="{{ asset('files/bower_components/jquery/dist/jquery.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('files/bower_components/jquery-ui/jquery-ui.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('files/bower_components/popper.js/dist/umd/popper.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('files/bower_components/bootstrap/dist/js/bootstrap.min.js') }}"></script>
    <!-- jquery slimscroll js -->
    <script type="text/javascript" src="{{ asset('files/bower_components/jquery-slimscroll/jquery.slimscroll.js') }}"></script>
    <!-- modernizr js -->
    <script type="text/javascript" src="{{ asset('files/bower_components/modernizr/modernizr.js') }}"></script>
    <script type="text/javascript" src="{{ asset('files/bower_components/modernizr/feature-detects/css-scrollbars.js') }}"></script>
    <!-- Custom js -->
    <script src="{{ asset('files/assets/js/pcoded.min.js') }}"></script>
    <script src="{{ asset('files/assets/js/vartical-layout.min.js') }}"></script>
    <script src="{{ asset('files/assets/js/jquery.mCustomScrollbar.concat.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('files/assets/js/script.js') }}"></script>
    <script type="text/javascript" src="{{ asset('files/assets/js/crfm-custom.js') }}"></script>
    @stack('scripts')
</body>

</html>
