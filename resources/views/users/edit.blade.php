@extends('layouts.template')

@section('title', 'Modifier Utilisateur - CRFM')

@section('page-header')
@section('page-title', 'Modifier l\'utilisateur')
@section('page-description', $user->name . ' - ' . $user->email)
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ route('users.index') }}">Utilisateurs</a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ route('users.show', $user) }}">{{ $user->name }}</a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Modifier</a></li>
    </ul>
@endsection
@endsection

@section('content')
<form method="POST" action="{{ route('users.update', $user) }}" class="needs-validation" novalidate>
    @csrf
    @method('PUT')
    
    <div class="row">
        <!-- Informations personnelles -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-user text-c-blue me-2"></i>Informations personnelles</h5>
                </div>
                <div class="card-block">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="required">Nom complet</label>
                                <input type="text" name="name" class="form-control @error('name') is-invalid @enderror" 
                                       value="{{ old('name', $user->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="required">Email</label>
                                <input type="email" name="email" class="form-control @error('email') is-invalid @enderror" 
                                       value="{{ old('email', $user->email) }}" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Téléphone</label>
                                <input type="text" name="telephone" class="form-control @error('telephone') is-invalid @enderror" 
                                       value="{{ old('telephone', $user->telephone) }}">
                                @error('telephone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="required">Rôle</label>
                                <select name="role" class="form-control @error('role') is-invalid @enderror" required>
                                    <option value="">Sélectionner un rôle</option>
                                    <option value="admin" {{ old('role', $user->role) == 'admin' ? 'selected' : '' }}>Administrateur</option>
                                    <option value="gestionnaire" {{ old('role', $user->role) == 'gestionnaire' ? 'selected' : '' }}>Gestionnaire</option>
                                    <option value="operateur" {{ old('role', $user->role) == 'operateur' ? 'selected' : '' }}>Opérateur</option>
                                </select>
                                @error('role')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Adresse</label>
                                <textarea name="adresse" class="form-control @error('adresse') is-invalid @enderror" 
                                          rows="3">{{ old('adresse', $user->adresse) }}</textarea>
                                @error('adresse')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Paramètres de sécurité -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-shield text-c-green me-2"></i>Sécurité</h5>
                </div>
                <div class="card-block">
                    <div class="alert alert-info">
                        <i class="feather icon-info me-2"></i>
                        Laissez vide pour conserver le mot de passe actuel
                    </div>
                    
                    <div class="form-group">
                        <label>Nouveau mot de passe</label>
                        <input type="password" name="password" class="form-control @error('password') is-invalid @enderror">
                        <small class="form-text text-muted">Minimum 8 caractères</small>
                        @error('password')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label>Confirmer le nouveau mot de passe</label>
                        <input type="password" name="password_confirmation" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" name="is_active" class="form-check-input" id="is_active" 
                                   value="1" {{ old('is_active', $user->is_active) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                Compte actif
                            </label>
                        </div>
                        <small class="form-text text-muted">L'utilisateur pourra se connecter</small>
                    </div>
                </div>
            </div>
            
            <!-- Informations sur les rôles -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-info text-c-blue me-2"></i>Informations du compte</h5>
                </div>
                <div class="card-block">
                    <div class="info-item">
                        <strong>Créé le :</strong><br>
                        <small class="text-muted">{{ $user->created_at->format('d/m/Y à H:i') }}</small>
                    </div>
                    <div class="info-item mt-3">
                        <strong>Dernière modification :</strong><br>
                        <small class="text-muted">{{ $user->updated_at->format('d/m/Y à H:i') }}</small>
                    </div>
                    @if($user->last_login_at)
                    <div class="info-item mt-3">
                        <strong>Dernière connexion :</strong><br>
                        <small class="text-muted">{{ $user->last_login_at->format('d/m/Y à H:i') }}</small>
                    </div>
                    @endif
                    <div class="info-item mt-3">
                        <strong>Statut actuel :</strong><br>
                        <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }}">
                            {{ $user->is_active ? 'Actif' : 'Inactif' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Boutons d'action -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-block text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="feather icon-save me-2"></i>Enregistrer les modifications
                    </button>
                    <a href="{{ route('users.show', $user) }}" class="btn btn-secondary btn-lg ms-2">
                        <i class="feather icon-x me-2"></i>Annuler
                    </a>
                    @if($user->id !== auth()->id())
                    <button type="button" class="btn btn-danger btn-lg ms-2" onclick="deleteUser()">
                        <i class="feather icon-trash me-2"></i>Supprimer
                    </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
</form>

@if($user->id !== auth()->id())
<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer cet utilisateur ?</p>
                <p><strong>{{ $user->name }}</strong> ({{ $user->email }})</p>
                <div class="alert alert-warning">
                    <i class="feather icon-alert-triangle me-2"></i>
                    Cette action est irréversible !
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form method="POST" action="{{ route('users.destroy', $user) }}" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Supprimer définitivement</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endif
@endsection

@push('styles')
<style>
.required::after {
    content: " *";
    color: red;
}
.form-group {
    margin-bottom: 1rem;
}
.card-block {
    padding: 1.5rem;
}
.info-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}
.info-item:last-child {
    border-bottom: none;
}
</style>
@endpush

@push('scripts')
<script>
// Validation du formulaire
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

function deleteUser() {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
@endpush
