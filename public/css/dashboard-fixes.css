/* Dashboard Text Visibility Fixes - FORCE VISIBILITY */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');

/* FORCE text visibility on ALL colored card backgrounds */
.card.bg-c-red *,
.card.bg-c-orange *,
.card.bg-c-purple *,
.card.bg-c-green *,
.card.bg-c-blue *,
.card.bg-c-pink *,
.card.bg-c-yellow * {
    color: #ffffff !important;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7) !important;
}

/* Specific targeting for text elements */
.card.bg-c-red h4, .card.bg-c-red h6,
.card.bg-c-orange h4, .card.bg-c-orange h6,
.card.bg-c-purple h4, .card.bg-c-purple h6,
.card.bg-c-green h4, .card.bg-c-green h6,
.card.bg-c-blue h4, .card.bg-c-blue h6,
.card.bg-c-pink h4, .card.bg-c-pink h6,
.card.bg-c-yellow h4, .card.bg-c-yellow h6 {
    color: #ffffff !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
    font-weight: 700 !important;
}

/* FORCE icons visibility */
.card.bg-c-red .feather,
.card.bg-c-orange .feather,
.card.bg-c-purple .feather,
.card.bg-c-green .feather,
.card.bg-c-blue .feather,
.card.bg-c-pink .feather,
.card.bg-c-yellow .feather,
.card .feather {
    color: #ffffff !important;
    opacity: 1 !important;
    filter: drop-shadow(1px 1px 2px rgba(0,0,0,0.8)) !important;
}

/* Ensure card headers are visible */
.card.bg-c-red h4,
.card.bg-c-red h5,
.card.bg-c-red h6,
.card.bg-c-orange h4,
.card.bg-c-orange h5,
.card.bg-c-orange h6,
.card.bg-c-purple h4,
.card.bg-c-purple h5,
.card.bg-c-purple h6,
.card.bg-c-green h4,
.card.bg-c-green h5,
.card.bg-c-green h6,
.card.bg-c-blue h4,
.card.bg-c-blue h5,
.card.bg-c-blue h6,
.card.bg-c-pink h4,
.card.bg-c-pink h5,
.card.bg-c-pink h6 {
    color: #ffffff !important;
    font-weight: 600 !important;
}

/* Ensure card footers are visible */
.card-footer.bg-c-red p,
.card-footer.bg-c-orange p,
.card-footer.bg-c-purple p,
.card-footer.bg-c-green p,
.card-footer.bg-c-blue p,
.card-footer.bg-c-pink p {
    color: #ffffff !important;
    margin-bottom: 0 !important;
}

/* Fix for specific problematic cards */
.card-block .f-w-600 {
    font-weight: 600 !important;
}

.card-block .m-b-0 {
    margin-bottom: 0 !important;
}

/* Ensure proper contrast for all dashboard cards */
.dashboard-stats .card {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: none;
}

.dashboard-stats .card-block {
    padding: 1.5rem;
}

/* Fix for text alignment and spacing */
.text-end {
    text-align: right !important;
}

.f-28 {
    font-size: 28px !important;
}

/* POLICES PERSONNALISÉES */
.dashboard-stats .card h4 {
    font-family: 'Poppins', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif !important;
    font-weight: 700 !important;
    font-size: 2.2rem !important;
    letter-spacing: -0.5px !important;
    line-height: 1.2 !important;
}

.dashboard-stats .card h6 {
    font-family: 'Inter', 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    letter-spacing: 0.25px !important;
    text-transform: uppercase !important;
    line-height: 1.4 !important;
}

/* Police pour les titres principaux */
.welcome-card h2 {
    font-family: 'Poppins', sans-serif !important;
    font-weight: 600 !important;
    letter-spacing: -0.5px !important;
    font-size: 1.75rem !important;
}

.welcome-card p {
    font-family: 'Inter', sans-serif !important;
    font-weight: 400 !important;
    font-size: 0.95rem !important;
}

/* Police pour les cartes d'événements */
.card-header h5 {
    font-family: 'Inter', sans-serif !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
    letter-spacing: 0.25px !important;
}

/* Police pour les boutons */
.btn {
    font-family: 'Inter', sans-serif !important;
    font-weight: 500 !important;
    letter-spacing: 0.25px !important;
    font-size: 0.875rem !important;
}

/* Police générale pour le contenu des cartes */
.card-block {
    font-family: 'Inter', sans-serif !important;
}

/* Responsive fixes */
@media (max-width: 768px) {
    .card-block {
        padding: 1rem;
    }

    .f-28 {
        font-size: 24px !important;
    }

    .dashboard-stats .card h4 {
        font-size: 1.8rem !important;
    }

    .dashboard-stats .card h6 {
        font-size: 0.8rem !important;
    }

    .welcome-card h2 {
        font-size: 1.5rem !important;
    }
}
