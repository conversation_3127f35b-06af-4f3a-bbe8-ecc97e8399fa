<!DOCTYPE html>
<html>
<head>
    <title>Test Chart.js</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <h1>Test Chart.js Simple</h1>
    <div style="width: 400px; height: 400px;">
        <canvas id="testChart"></canvas>
    </div>

    <script>
        console.log('=== TEST CHART.JS ===');
        
        // Test si Chart.js est chargé
        console.log('Chart.js disponible:', typeof Chart !== 'undefined');
        
        // Récupérer le canvas
        const ctx = document.getElementById('testChart');
        console.log('Canvas trouvé:', !!ctx);
        
        if (ctx && typeof Chart !== 'undefined') {
            try {
                console.log('Création du graphique test...');
                
                const chart = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Vieillesse', 'Anticipée', 'Invalidité', 'Survivant'],
                        datasets: [{
                            data: [4, 2, 0, 0],
                            backgroundColor: ['#1f77b4', '#2ca02c', '#ff7f0e', '#d62728']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        animation: false,
                        plugins: {
                            legend: { display: true }
                        }
                    }
                });
                
                console.log('✅ Graphique créé avec succès !');
                console.log('Chart instance:', chart);
                
            } catch (error) {
                console.error('❌ Erreur:', error);
                document.body.innerHTML += '<div style="color: red;">Erreur: ' + error.message + '</div>';
            }
        } else {
            console.error('❌ Canvas ou Chart.js non disponible');
            document.body.innerHTML += '<div style="color: red;">Canvas ou Chart.js non disponible</div>';
        }
    </script>
</body>
</html>
