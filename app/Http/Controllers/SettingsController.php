<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class SettingsController extends Controller
{
    /**
     * Display the settings page.
     */
    public function index(): View
    {
        // Version simplifiée pour debug
        $settings = [
            'app_name' => 'CRFM',
            'app_description' => 'Système de gestion des retraites',
            'organization_name' => 'Caisse de Retraite des Fonctionnaires du Mali',
            'organization_address' => '',
            'organization_phone' => '',
            'organization_email' => '',
            'organization_website' => '',
            'logo' => '',
            'favicon' => '',
            'timezone' => 'Africa/Bamako',
            'date_format' => 'd/m/Y',
            'currency' => 'FCFA',
            'language' => 'fr',
            'items_per_page' => 15,
            'session_timeout' => 120,
            'backup_frequency' => 'weekly',
            'maintenance_mode' => false,
            'registration_enabled' => false,
            'email_notifications' => true,
            'sms_notifications' => false,
        ];

        return view('settings.index', compact('settings'));
    }

    /**
     * Update system settings.
     */
    public function update(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'app_name' => ['required', 'string', 'max:255'],
            'app_description' => ['nullable', 'string', 'max:500'],
            'organization_name' => ['required', 'string', 'max:255'],
            'organization_address' => ['nullable', 'string', 'max:1000'],
            'organization_phone' => ['nullable', 'string', 'max:20'],
            'organization_email' => ['nullable', 'email', 'max:255'],
            'organization_website' => ['nullable', 'url', 'max:255'],
            'logo' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
            'favicon' => ['nullable', 'image', 'mimes:ico,png', 'max:512'],
            'timezone' => ['required', 'string', 'max:50'],
            'date_format' => ['required', 'string', 'max:20'],
            'currency' => ['required', 'string', 'max:10'],
            'language' => ['required', 'string', 'max:10'],
            'items_per_page' => ['required', 'integer', 'min:5', 'max:100'],
            'session_timeout' => ['required', 'integer', 'min:15', 'max:1440'],
            'backup_frequency' => ['required', 'string', 'in:daily,weekly,monthly'],
            'maintenance_mode' => ['boolean'],
            'registration_enabled' => ['boolean'],
            'email_notifications' => ['boolean'],
            'sms_notifications' => ['boolean'],
        ]);

        try {
            // Handle file uploads
            if ($request->hasFile('logo')) {
                $logoPath = $request->file('logo')->store('settings', 'public');
                $validated['logo'] = $logoPath;
            }

            if ($request->hasFile('favicon')) {
                $faviconPath = $request->file('favicon')->store('settings', 'public');
                $validated['favicon'] = $faviconPath;
            }

            // Handle checkboxes
            $validated['maintenance_mode'] = $request->has('maintenance_mode');
            $validated['registration_enabled'] = $request->has('registration_enabled');
            $validated['email_notifications'] = $request->has('email_notifications');
            $validated['sms_notifications'] = $request->has('sms_notifications');

            // Save settings to cache and config
            foreach ($validated as $key => $value) {
                Cache::forever("settings.{$key}", $value);
            }

            return redirect()->route('settings.index')
                ->with('success', 'Paramètres mis à jour avec succès.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Erreur lors de la mise à jour des paramètres.');
        }
    }

    /**
     * Get all settings with default values.
     */
    private function getSettings(): array
    {
        return [
            'app_name' => Cache::get('settings.app_name', config('app.name', 'CRFM')),
            'app_description' => Cache::get('settings.app_description', 'Système de gestion des retraites'),
            'organization_name' => Cache::get('settings.organization_name', 'Caisse de Retraite des Fonctionnaires du Mali'),
            'organization_address' => Cache::get('settings.organization_address', ''),
            'organization_phone' => Cache::get('settings.organization_phone', ''),
            'organization_email' => Cache::get('settings.organization_email', ''),
            'organization_website' => Cache::get('settings.organization_website', ''),
            'logo' => Cache::get('settings.logo', ''),
            'favicon' => Cache::get('settings.favicon', ''),
            'timezone' => Cache::get('settings.timezone', 'Africa/Bamako'),
            'date_format' => Cache::get('settings.date_format', 'd/m/Y'),
            'currency' => Cache::get('settings.currency', 'FCFA'),
            'language' => Cache::get('settings.language', 'fr'),
            'items_per_page' => Cache::get('settings.items_per_page', 15),
            'session_timeout' => Cache::get('settings.session_timeout', 120),
            'backup_frequency' => Cache::get('settings.backup_frequency', 'weekly'),
            'maintenance_mode' => Cache::get('settings.maintenance_mode', false),
            'registration_enabled' => Cache::get('settings.registration_enabled', false),
            'email_notifications' => Cache::get('settings.email_notifications', true),
            'sms_notifications' => Cache::get('settings.sms_notifications', false),
        ];
    }

    /**
     * Clear application cache.
     */
    public function clearCache(): RedirectResponse
    {
        try {
            Cache::flush();
            
            return redirect()->back()
                ->with('success', 'Cache vidé avec succès.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Erreur lors du vidage du cache.');
        }
    }

    /**
     * Test email configuration.
     */
    public function testEmail(Request $request): RedirectResponse
    {
        $request->validate([
            'test_email' => ['required', 'email']
        ]);

        try {
            // Logique d'envoi d'email de test
            // Mail::to($request->test_email)->send(new TestMail());
            
            return redirect()->back()
                ->with('success', 'Email de test envoyé avec succès.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Erreur lors de l\'envoi de l\'email de test.');
        }
    }

    /**
     * Export system settings.
     */
    public function export()
    {
        $settings = $this->getSettings();
        
        $filename = 'settings_' . date('Y-m-d_H-i-s') . '.json';
        
        return response()->json($settings)
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    /**
     * Import system settings.
     */
    public function import(Request $request): RedirectResponse
    {
        $request->validate([
            'settings_file' => ['required', 'file', 'mimes:json']
        ]);

        try {
            $content = file_get_contents($request->file('settings_file')->getRealPath());
            $settings = json_decode($content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Fichier JSON invalide');
            }

            // Validate and save settings
            foreach ($settings as $key => $value) {
                Cache::forever("settings.{$key}", $value);
            }

            return redirect()->back()
                ->with('success', 'Paramètres importés avec succès.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Erreur lors de l\'importation des paramètres.');
        }
    }
}
