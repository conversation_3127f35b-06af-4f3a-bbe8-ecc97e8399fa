<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;
use App\Services\OutilsService;
use Illuminate\Http\JsonResponse;

class OutilsController extends Controller
{
    protected OutilsService $outilsService;

    public function __construct(OutilsService $outilsService)
    {
        $this->outilsService = $outilsService;
    }

    /**
     * Display the tools page
     */
    public function index(): View
    {
        $data = [
            'statistiquesVerification' => $this->outilsService->getStatistiquesVerification(),
            'tachesRecentes' => $this->outilsService->getTachesRecentes(),
        ];

        return view('pages.outils', $data);
    }

    /**
     * Calculate pension via AJAX
     */
    public function calculerPension(Request $request): JsonResponse
    {
        $request->validate([
            'salaire_reference' => 'required|numeric|min:0',
            'duree_cotisation' => 'required|numeric|min:0',
            'type_pension' => 'required|in:vieillesse,anticipee,invalidite,survivant',
            'age_depart' => 'nullable|numeric|min:50|max:70'
        ]);

        $resultat = $this->outilsService->calculerPension(
            $request->salaire_reference,
            $request->duree_cotisation,
            $request->type_pension,
            $request->age_depart
        );

        return response()->json([
            'success' => true,
            'data' => $resultat
        ]);
    }

    /**
     * Generate number via AJAX
     */
    public function genererNumero(Request $request): JsonResponse
    {
        $request->validate([
            'type' => 'required|in:affiliation,declaration,preliquidation,adherent,cotisation'
        ]);

        $numero = $this->outilsService->genererNumero($request->type);

        return response()->json([
            'success' => true,
            'numero' => $numero
        ]);
    }

    /**
     * Verify data integrity
     */
    public function verifierDonnees(): JsonResponse
    {
        $resultats = $this->outilsService->verifierIntegriteDonnees();

        return response()->json([
            'success' => true,
            'data' => $resultats
        ]);
    }

    /**
     * Import data from file
     */
    public function importerDonnees(Request $request): JsonResponse
    {
        $request->validate([
            'type' => 'required|in:adherents,cotisations,employeurs',
            'fichier' => 'required|file|mimes:xlsx,xls,csv|max:10240'
        ]);

        try {
            $resultat = $this->outilsService->importerDonnees(
                $request->type,
                $request->file('fichier')
            );

            return response()->json([
                'success' => true,
                'message' => 'Import réussi',
                'data' => $resultat
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'import : ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * Export data
     */
    public function exporterDonnees(Request $request)
    {
        $request->validate([
            'type' => 'required|in:adherents,cotisations,declarations,pensions',
            'format' => 'required|in:excel,pdf,csv'
        ]);

        return $this->outilsService->exporterDonnees(
            $request->type,
            $request->format
        );
    }
}
