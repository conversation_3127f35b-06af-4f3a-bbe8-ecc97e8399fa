<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;

class NotificationController extends Controller
{
    /**
     * Display the notification settings page.
     */
    public function index(): View
    {
        $settings = $this->getNotificationSettings();
        $templates = $this->getNotificationTemplates();
        $recentNotifications = $this->getRecentNotifications();
        
        return view('notifications.index', compact('settings', 'templates', 'recentNotifications'));
    }

    /**
     * Update notification settings.
     */
    public function update(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'email_enabled' => ['boolean'],
            'sms_enabled' => ['boolean'],
            'push_enabled' => ['boolean'],
            'email_from_name' => ['required', 'string', 'max:255'],
            'email_from_address' => ['required', 'email', 'max:255'],
            'email_reply_to' => ['nullable', 'email', 'max:255'],
            'sms_provider' => ['nullable', 'string', 'in:twilio,nexmo,orange'],
            'sms_api_key' => ['nullable', 'string', 'max:255'],
            'sms_api_secret' => ['nullable', 'string', 'max:255'],
            'sms_sender_id' => ['nullable', 'string', 'max:20'],
            'notification_frequency' => ['required', 'string', 'in:immediate,hourly,daily,weekly'],
            'max_notifications_per_user' => ['required', 'integer', 'min:1', 'max:100'],
            'notification_retention_days' => ['required', 'integer', 'min:1', 'max:365'],
            // Notifications par type
            'notify_new_adherent' => ['boolean'],
            'notify_cotisation_due' => ['boolean'],
            'notify_pension_request' => ['boolean'],
            'notify_user_login' => ['boolean'],
            'notify_system_error' => ['boolean'],
            'notify_backup_status' => ['boolean'],
        ]);

        try {
            // Handle checkboxes
            $checkboxFields = [
                'email_enabled', 'sms_enabled', 'push_enabled',
                'notify_new_adherent', 'notify_cotisation_due', 'notify_pension_request',
                'notify_user_login', 'notify_system_error', 'notify_backup_status'
            ];
            
            foreach ($checkboxFields as $field) {
                $validated[$field] = $request->has($field);
            }

            // Save settings to cache
            foreach ($validated as $key => $value) {
                Cache::forever("notifications.{$key}", $value);
            }

            return redirect()->route('notifications.index')
                ->with('success', 'Paramètres de notification mis à jour avec succès.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Erreur lors de la mise à jour des paramètres de notification.');
        }
    }

    /**
     * Test email configuration.
     */
    public function testEmail(Request $request): RedirectResponse
    {
        $request->validate([
            'test_email' => ['required', 'email']
        ]);

        try {
            // Simuler l'envoi d'un email de test
            // Mail::to($request->test_email)->send(new TestNotificationMail());
            
            return redirect()->back()
                ->with('success', 'Email de test envoyé avec succès à ' . $request->test_email);
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Erreur lors de l\'envoi de l\'email de test : ' . $e->getMessage());
        }
    }

    /**
     * Test SMS configuration.
     */
    public function testSms(Request $request): RedirectResponse
    {
        $request->validate([
            'test_phone' => ['required', 'string', 'max:20']
        ]);

        try {
            // Simuler l'envoi d'un SMS de test
            // SMS::to($request->test_phone)->send('Test de notification SMS depuis CRFM');
            
            return redirect()->back()
                ->with('success', 'SMS de test envoyé avec succès au ' . $request->test_phone);
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Erreur lors de l\'envoi du SMS de test : ' . $e->getMessage());
        }
    }

    /**
     * Send bulk notification.
     */
    public function sendBulk(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'recipients' => ['required', 'string', 'in:all,admins,gestionnaires,operateurs'],
            'type' => ['required', 'string', 'in:email,sms,both'],
            'subject' => ['required', 'string', 'max:255'],
            'message' => ['required', 'string', 'max:1000'],
            'priority' => ['required', 'string', 'in:low,normal,high,urgent']
        ]);

        try {
            // Logique d'envoi en masse
            $recipientCount = $this->getBulkRecipientCount($validated['recipients']);
            
            // Simuler l'envoi
            // $this->sendBulkNotification($validated);
            
            return redirect()->back()
                ->with('success', "Notification envoyée à {$recipientCount} destinataire(s).");
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Erreur lors de l\'envoi de la notification en masse.');
        }
    }

    /**
     * Get notification settings with default values.
     */
    private function getNotificationSettings(): array
    {
        return [
            'email_enabled' => Cache::get('notifications.email_enabled', true),
            'sms_enabled' => Cache::get('notifications.sms_enabled', false),
            'push_enabled' => Cache::get('notifications.push_enabled', false),
            'email_from_name' => Cache::get('notifications.email_from_name', 'CRFM'),
            'email_from_address' => Cache::get('notifications.email_from_address', '<EMAIL>'),
            'email_reply_to' => Cache::get('notifications.email_reply_to', ''),
            'sms_provider' => Cache::get('notifications.sms_provider', 'orange'),
            'sms_api_key' => Cache::get('notifications.sms_api_key', ''),
            'sms_api_secret' => Cache::get('notifications.sms_api_secret', ''),
            'sms_sender_id' => Cache::get('notifications.sms_sender_id', 'CRFM'),
            'notification_frequency' => Cache::get('notifications.notification_frequency', 'immediate'),
            'max_notifications_per_user' => Cache::get('notifications.max_notifications_per_user', 50),
            'notification_retention_days' => Cache::get('notifications.notification_retention_days', 30),
            'notify_new_adherent' => Cache::get('notifications.notify_new_adherent', true),
            'notify_cotisation_due' => Cache::get('notifications.notify_cotisation_due', true),
            'notify_pension_request' => Cache::get('notifications.notify_pension_request', true),
            'notify_user_login' => Cache::get('notifications.notify_user_login', false),
            'notify_system_error' => Cache::get('notifications.notify_system_error', true),
            'notify_backup_status' => Cache::get('notifications.notify_backup_status', true),
        ];
    }

    /**
     * Get notification templates.
     */
    private function getNotificationTemplates(): array
    {
        return [
            'new_adherent' => [
                'name' => 'Nouvel adhérent',
                'subject' => 'Nouveau dossier d\'adhérent créé',
                'description' => 'Notification lors de la création d\'un nouveau dossier'
            ],
            'cotisation_due' => [
                'name' => 'Cotisation échue',
                'subject' => 'Rappel de cotisation',
                'description' => 'Rappel automatique pour les cotisations en retard'
            ],
            'pension_request' => [
                'name' => 'Demande de pension',
                'subject' => 'Nouvelle demande de pension',
                'description' => 'Notification pour les nouvelles demandes de pension'
            ],
            'system_error' => [
                'name' => 'Erreur système',
                'subject' => 'Alerte système',
                'description' => 'Notification en cas d\'erreur critique'
            ],
            'backup_status' => [
                'name' => 'Statut de sauvegarde',
                'subject' => 'Rapport de sauvegarde',
                'description' => 'Rapport quotidien des sauvegardes'
            ]
        ];
    }

    /**
     * Get recent notifications (mock data).
     */
    private function getRecentNotifications(): array
    {
        return [
            [
                'type' => 'email',
                'recipient' => '<EMAIL>',
                'subject' => 'Nouveau dossier d\'adhérent créé',
                'status' => 'sent',
                'sent_at' => now()->subMinutes(15),
                'priority' => 'normal'
            ],
            [
                'type' => 'sms',
                'recipient' => '+223 70 00 00 00',
                'subject' => 'Rappel de cotisation',
                'status' => 'failed',
                'sent_at' => now()->subHour(),
                'priority' => 'high'
            ],
            [
                'type' => 'email',
                'recipient' => '<EMAIL>',
                'subject' => 'Rapport de sauvegarde',
                'status' => 'sent',
                'sent_at' => now()->subHours(2),
                'priority' => 'low'
            ]
        ];
    }

    /**
     * Get recipient count for bulk notifications.
     */
    private function getBulkRecipientCount(string $recipients): int
    {
        return match($recipients) {
            'all' => \App\Models\User::count(),
            'admins' => \App\Models\User::where('role', 'admin')->count(),
            'gestionnaires' => \App\Models\User::where('role', 'gestionnaire')->count(),
            'operateurs' => \App\Models\User::where('role', 'operateur')->count(),
            default => 0
        };
    }
}
