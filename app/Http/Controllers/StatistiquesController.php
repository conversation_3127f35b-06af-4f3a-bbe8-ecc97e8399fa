<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;
use App\Services\StatistiquesService;

class StatistiquesController extends Controller
{
    protected StatistiquesService $statistiquesService;

    public function __construct(StatistiquesService $statistiquesService)
    {
        $this->statistiquesService = $statistiquesService;
    }

    /**
     * Display the statistics page
     */
    public function index(): View
    {
        $data = [
            'kpis' => $this->statistiquesService->getKPIs(),
            'evolutionData' => $this->statistiquesService->getEvolutionData(),
            'repartitionData' => $this->statistiquesService->getRepartitionData(),
            'topEmployeurs' => $this->statistiquesService->getTopEmployeurs(),
            'indicateurs' => $this->statistiquesService->getIndicateursPerformance(),
        ];

        return view('pages.statistiques', $data);
    }

    /**
     * Get chart data via AJAX
     */
    public function getChartData(Request $request)
    {
        $type = $request->get('type', 'evolution');
        $period = $request->get('period', '12');
        
        $data = match($type) {
            'evolution' => $this->statistiquesService->getEvolutionData($period),
            'repartition' => $this->statistiquesService->getRepartitionData(),
            default => []
        };
        
        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Export statistics data
     */
    public function export(Request $request)
    {
        $type = $request->get('type', 'monthly');
        
        return $this->statistiquesService->exportData($type);
    }
}
