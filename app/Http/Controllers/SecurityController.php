<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class SecurityController extends Controller
{
    /**
     * Display the security settings page.
     */
    public function index(): View
    {
        $settings = $this->getSecuritySettings();
        $loginAttempts = $this->getRecentLoginAttempts();
        $activeSessions = $this->getActiveSessions();
        
        return view('security.index', compact('settings', 'loginAttempts', 'activeSessions'));
    }

    /**
     * Update security settings.
     */
    public function update(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'password_min_length' => ['required', 'integer', 'min:6', 'max:50'],
            'password_require_uppercase' => ['boolean'],
            'password_require_lowercase' => ['boolean'],
            'password_require_numbers' => ['boolean'],
            'password_require_symbols' => ['boolean'],
            'password_expiry_days' => ['nullable', 'integer', 'min:0', 'max:365'],
            'max_login_attempts' => ['required', 'integer', 'min:3', 'max:10'],
            'lockout_duration' => ['required', 'integer', 'min:5', 'max:1440'],
            'session_lifetime' => ['required', 'integer', 'min:15', 'max:1440'],
            'force_https' => ['boolean'],
            'two_factor_enabled' => ['boolean'],
            'password_history_count' => ['nullable', 'integer', 'min:0', 'max:10'],
            'auto_logout_inactive' => ['boolean'],
            'inactive_timeout' => ['nullable', 'integer', 'min:5', 'max:120'],
        ]);

        try {
            // Handle checkboxes
            $validated['password_require_uppercase'] = $request->has('password_require_uppercase');
            $validated['password_require_lowercase'] = $request->has('password_require_lowercase');
            $validated['password_require_numbers'] = $request->has('password_require_numbers');
            $validated['password_require_symbols'] = $request->has('password_require_symbols');
            $validated['force_https'] = $request->has('force_https');
            $validated['two_factor_enabled'] = $request->has('two_factor_enabled');
            $validated['auto_logout_inactive'] = $request->has('auto_logout_inactive');

            // Save settings to cache
            foreach ($validated as $key => $value) {
                Cache::forever("security.{$key}", $value);
            }

            return redirect()->route('security.index')
                ->with('success', 'Paramètres de sécurité mis à jour avec succès.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Erreur lors de la mise à jour des paramètres de sécurité.');
        }
    }

    /**
     * Force password reset for all users.
     */
    public function forcePasswordReset(): RedirectResponse
    {
        try {
            // Marquer tous les utilisateurs pour un changement de mot de passe obligatoire
            User::query()->update(['password_changed_at' => null]);
            
            return redirect()->back()
                ->with('success', 'Réinitialisation de mot de passe forcée pour tous les utilisateurs.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Erreur lors de la réinitialisation forcée des mots de passe.');
        }
    }

    /**
     * Clear all active sessions except current.
     */
    public function clearAllSessions(): RedirectResponse
    {
        try {
            // Logique pour vider toutes les sessions actives
            // Cette fonctionnalité nécessiterait une implémentation plus avancée
            
            return redirect()->back()
                ->with('success', 'Toutes les sessions actives ont été fermées.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Erreur lors de la fermeture des sessions.');
        }
    }

    /**
     * Get security settings with default values.
     */
    private function getSecuritySettings(): array
    {
        return [
            'password_min_length' => Cache::get('security.password_min_length', 8),
            'password_require_uppercase' => Cache::get('security.password_require_uppercase', true),
            'password_require_lowercase' => Cache::get('security.password_require_lowercase', true),
            'password_require_numbers' => Cache::get('security.password_require_numbers', true),
            'password_require_symbols' => Cache::get('security.password_require_symbols', false),
            'password_expiry_days' => Cache::get('security.password_expiry_days', 90),
            'max_login_attempts' => Cache::get('security.max_login_attempts', 5),
            'lockout_duration' => Cache::get('security.lockout_duration', 15),
            'session_lifetime' => Cache::get('security.session_lifetime', 120),
            'force_https' => Cache::get('security.force_https', true),
            'two_factor_enabled' => Cache::get('security.two_factor_enabled', false),
            'password_history_count' => Cache::get('security.password_history_count', 5),
            'auto_logout_inactive' => Cache::get('security.auto_logout_inactive', true),
            'inactive_timeout' => Cache::get('security.inactive_timeout', 30),
        ];
    }

    /**
     * Get recent login attempts (mock data for now).
     */
    private function getRecentLoginAttempts(): array
    {
        return [
            [
                'user' => '<EMAIL>',
                'ip' => '*************',
                'status' => 'success',
                'timestamp' => now()->subMinutes(5),
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ],
            [
                'user' => '<EMAIL>',
                'ip' => '*************',
                'status' => 'failed',
                'timestamp' => now()->subMinutes(15),
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ],
            [
                'user' => '<EMAIL>',
                'ip' => '*************',
                'status' => 'success',
                'timestamp' => now()->subHour(),
                'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
            ]
        ];
    }

    /**
     * Get active sessions (mock data for now).
     */
    private function getActiveSessions(): array
    {
        return [
            [
                'user' => 'Admin CRFM',
                'ip' => '*************',
                'location' => 'Bamako, Mali',
                'device' => 'Windows 10 - Chrome',
                'last_activity' => now()->subMinutes(2),
                'is_current' => true
            ],
            [
                'user' => 'Gestionnaire',
                'ip' => '*************',
                'location' => 'Bamako, Mali',
                'device' => 'macOS - Safari',
                'last_activity' => now()->subMinutes(30),
                'is_current' => false
            ]
        ];
    }

    /**
     * Generate security report.
     */
    public function generateReport()
    {
        $report = [
            'total_users' => User::count(),
            'active_users' => User::where('is_active', true)->count(),
            'admin_users' => User::where('role', 'admin')->count(),
            'recent_logins' => count($this->getRecentLoginAttempts()),
            'failed_attempts' => collect($this->getRecentLoginAttempts())->where('status', 'failed')->count(),
            'active_sessions' => count($this->getActiveSessions()),
            'security_score' => $this->calculateSecurityScore(),
        ];

        return response()->json($report);
    }

    /**
     * Calculate security score based on current settings.
     */
    private function calculateSecurityScore(): int
    {
        $settings = $this->getSecuritySettings();
        $score = 0;

        // Password policy (40 points max)
        if ($settings['password_min_length'] >= 8) $score += 10;
        if ($settings['password_require_uppercase']) $score += 5;
        if ($settings['password_require_lowercase']) $score += 5;
        if ($settings['password_require_numbers']) $score += 10;
        if ($settings['password_require_symbols']) $score += 10;

        // Session security (30 points max)
        if ($settings['session_lifetime'] <= 120) $score += 10;
        if ($settings['auto_logout_inactive']) $score += 10;
        if ($settings['force_https']) $score += 10;

        // Access control (30 points max)
        if ($settings['max_login_attempts'] <= 5) $score += 10;
        if ($settings['lockout_duration'] >= 15) $score += 10;
        if ($settings['two_factor_enabled']) $score += 10;

        return $score;
    }
}
