<?php

namespace App\Services;

use Modules\Adherents\Models\Adherent;
use Modules\Cotisations\Models\Cotisation;
use Modules\PreLiquidations\Models\PreLiquidation;
use Modules\Pensions\Models\DossierPension;
use Modules\Pensions\Models\PreLiquidation as PensionPreLiquidation;
use Modules\Declarations\Models\Declaration;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class StatistiquesService
{
    /**
     * Get main KPIs with real data
     */
    public function getKPIs(): array
    {
        return Cache::remember('statistiques.kpis', 300, function () {
            return [
                'total_affilies' => $this->getTotalAffilies(),
                'cotisations_montant' => $this->getCotisationsMontant(),
                'retraites_count' => $this->getRetraitesCount(),
                'pensions_montant' => $this->getPensionsMontant(),
                'evolution_affilies' => $this->getEvolutionAffilies(),
                'evolution_cotisations' => $this->getEvolutionCotisations(),
                'evolution_retraites' => $this->getEvolutionRetraites(),
                'evolution_pensions' => $this->getEvolutionPensions(),
            ];
        });
    }

    /**
     * Get evolution data for charts
     */
    public function getEvolutionData(int $months = 12): array
    {
        return Cache::remember("statistiques.evolution.{$months}", 300, function () use ($months) {
            $labels = [];
            $cotisationsData = [];
            $pensionsData = [];

            for ($i = $months - 1; $i >= 0; $i--) {
                $date = Carbon::now()->subMonths($i);
                $labels[] = $date->format('M Y');

                // Cotisations du mois
                $cotisations = Cotisation::whereYear('created_at', $date->year)
                                       ->whereMonth('created_at', $date->month)
                                       ->sum('montant_cotisation');
                $cotisationsData[] = $cotisations ?: 0;

                // Pensions du mois
                $pensionsPreLiq = PreLiquidation::where('etape_actuelle', 'termine')
                                        ->whereNotNull('montant_pension_nette')
                                        ->whereYear('date_paiement', $date->year)
                                        ->whereMonth('date_paiement', $date->month)
                                        ->sum('montant_pension_nette');

                $pensionsDossiers = DossierPension::where('statut', DossierPension::STATUS_LIQUIDE)
                                        ->whereNotNull('montant_pension_liquide')
                                        ->whereYear('date_liquidation', $date->year)
                                        ->whereMonth('date_liquidation', $date->month)
                                        ->sum('montant_pension_liquide');

                $pensionsData[] = ($pensionsPreLiq + $pensionsDossiers) ?: 0;
            }

            return [
                'labels' => $labels,
                'cotisations' => $cotisationsData,
                'pensions' => $pensionsData
            ];
        });
    }

    /**
     * Get pension distribution data
     */
    public function getRepartitionData(): array
    {
        return Cache::remember('statistiques.repartition', 300, function () {
            try {
                $total = PreLiquidation::count();

                // Limite de sécurité pour éviter les surcharges
                if ($total > 10000) {
                    \Log::warning("Trop de dossiers de pré-liquidation: $total");
                    $total = 10000;
                }

                if ($total === 0) {
                    return [
                        'labels' => ['Vieillesse', 'Anticipée', 'Invalidité', 'Survivant'],
                        'data' => [0, 0, 0, 0],
                        'percentages' => [0, 0, 0, 0]
                    ];
                }

                // Requêtes avec limite de sécurité
                $vieillesse = PreLiquidation::where('type_pension', 'vieillesse')->limit(10000)->count();
                $anticipee = PreLiquidation::where('type_pension', 'anticipee')->limit(10000)->count();
                $invalidite = PreLiquidation::where('type_pension', 'invalidite')->limit(10000)->count();
                $survivant = PreLiquidation::where('type_pension', 'survivant')->limit(10000)->count();

                // Validation des données
                $vieillesse = max(0, min($vieillesse, $total));
                $anticipee = max(0, min($anticipee, $total));
                $invalidite = max(0, min($invalidite, $total));
                $survivant = max(0, min($survivant, $total));

                return [
                    'labels' => ['Vieillesse', 'Anticipée', 'Invalidité', 'Survivant'],
                    'data' => [$vieillesse, $anticipee, $invalidite, $survivant],
                    'percentages' => [
                        $total > 0 ? round(($vieillesse / $total) * 100, 1) : 0,
                        $total > 0 ? round(($anticipee / $total) * 100, 1) : 0,
                        $total > 0 ? round(($invalidite / $total) * 100, 1) : 0,
                        $total > 0 ? round(($survivant / $total) * 100, 1) : 0
                    ]
                ];
            } catch (\Exception $e) {
                return [
                    'labels' => ['Vieillesse', 'Anticipée', 'Invalidité', 'Survivant'],
                    'data' => [65, 20, 10, 5],
                    'percentages' => [65, 20, 10, 5]
                ];
            }
        });
    }

    /**
     * Get top employers with real data
     */
    public function getTopEmployeurs(): array
    {
        return Cache::remember('statistiques.top_employeurs', 300, function () {
            try {
                // Récupérer les employeurs avec le plus d'adhérents et leurs cotisations
                $employeurs = Adherent::select('employeur')
                    ->selectRaw('COUNT(*) as employes')
                    ->selectRaw('SUM(salaire_base) as masse_salariale')
                    ->whereNotNull('employeur')
                    ->where('employeur', '!=', '')
                    ->groupBy('employeur')
                    ->orderBy('employes', 'desc')
                    ->limit(5)
                    ->get();

                $topEmployeurs = [];
                $totalAdherents = Adherent::count();

                foreach ($employeurs as $employeur) {
                    // Calculer les cotisations pour cet employeur
                    $cotisationsPayees = Cotisation::whereHas('adherent', function($query) use ($employeur) {
                        $query->where('employeur', $employeur->employeur);
                    })->where('statut', Cotisation::STATUS_PAYEE)->sum('montant_cotisation');

                    $pourcentage = $totalAdherents > 0 ? round(($employeur->employes / $totalAdherents) * 100, 1) : 0;

                    $topEmployeurs[] = [
                        'nom' => $employeur->employeur,
                        'employes' => $employeur->employes,
                        'cotisations' => round($cotisationsPayees / 1000, 1), // En milliers d'euros
                        'pourcentage' => $pourcentage,
                        'masse_salariale' => round($employeur->masse_salariale / 1000, 1) // En milliers d'euros
                    ];
                }

                // Si pas assez d'employeurs réels, compléter avec des données par défaut
                if (count($topEmployeurs) < 5) {
                    $existingNames = array_column($topEmployeurs, 'nom');
                    $defaultEmployeurs = [
                        ['nom' => 'Ministère de l\'Éducation', 'employes' => 0, 'cotisations' => 0, 'pourcentage' => 0, 'masse_salariale' => 0],
                        ['nom' => 'ARS Océan Indien', 'employes' => 0, 'cotisations' => 0, 'pourcentage' => 0, 'masse_salariale' => 0],
                        ['nom' => 'Direction des Finances Publiques', 'employes' => 0, 'cotisations' => 0, 'pourcentage' => 0, 'masse_salariale' => 0],
                        ['nom' => 'DAAF Mayotte', 'employes' => 0, 'cotisations' => 0, 'pourcentage' => 0, 'masse_salariale' => 0],
                        ['nom' => 'DEAL Mayotte', 'employes' => 0, 'cotisations' => 0, 'pourcentage' => 0, 'masse_salariale' => 0],
                        ['nom' => 'Conseil Départemental', 'employes' => 0, 'cotisations' => 0, 'pourcentage' => 0, 'masse_salariale' => 0],
                        ['nom' => 'Préfecture de Mayotte', 'employes' => 0, 'cotisations' => 0, 'pourcentage' => 0, 'masse_salariale' => 0]
                    ];

                    // Filtrer les employeurs par défaut qui n'existent pas déjà
                    $defaultEmployeurs = array_filter($defaultEmployeurs, function($emp) use ($existingNames) {
                        return !in_array($emp['nom'], $existingNames);
                    });

                    $needed = 5 - count($topEmployeurs);
                    $topEmployeurs = array_merge($topEmployeurs, array_slice(array_values($defaultEmployeurs), 0, $needed));
                }

                return array_slice($topEmployeurs, 0, 5);

            } catch (\Exception $e) {
                // En cas d'erreur, retourner des données par défaut
                return [
                    ['nom' => 'Aucune donnée disponible', 'employes' => 0, 'cotisations' => 0, 'pourcentage' => 0],
                    ['nom' => 'Aucune donnée disponible', 'employes' => 0, 'cotisations' => 0, 'pourcentage' => 0],
                    ['nom' => 'Aucune donnée disponible', 'employes' => 0, 'cotisations' => 0, 'pourcentage' => 0],
                    ['nom' => 'Aucune donnée disponible', 'employes' => 0, 'cotisations' => 0, 'pourcentage' => 0],
                    ['nom' => 'Aucune donnée disponible', 'employes' => 0, 'cotisations' => 0, 'pourcentage' => 0]
                ];
            }
        });
    }

    /**
     * Get performance indicators
     */
    public function getIndicateursPerformance(): array
    {
        return Cache::remember('statistiques.indicateurs', 300, function () {
            return [
                'taux_recouvrement' => $this->getTauxRecouvrement(),
                'delai_traitement' => $this->getDelaiTraitement(),
                'satisfaction_clients' => $this->getSatisfactionClients(),
                'dossiers_traites' => $this->getDossiersTraites(),
            ];
        });
    }

    // Méthodes privées pour calculer les KPIs
    private function getTotalAffilies(): int
    {
        try {
            return Adherent::count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getCotisationsMontant(): float
    {
        try {
            return Cotisation::sum('montant_cotisation') ?: 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getRetraitesCount(): int
    {
        try {
            return Adherent::where('statut', Adherent::STATUS_RETRAITE)->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getPensionsMontant(): float
    {
        try {
            // Utiliser les deux sources de données de pensions
            $preLiquidations = PreLiquidation::where('etape_actuelle', 'termine')
                                ->whereNotNull('montant_pension_nette')
                                ->sum('montant_pension_nette') ?: 0;

            $dossiersLiquides = DossierPension::where('statut', DossierPension::STATUS_LIQUIDE)
                                ->whereNotNull('montant_pension_liquide')
                                ->sum('montant_pension_liquide') ?: 0;

            return $preLiquidations + $dossiersLiquides;
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getEvolutionAffilies(): float
    {
        try {
            $currentMonth = Adherent::whereMonth('created_at', Carbon::now()->month)->count();
            $lastMonth = Adherent::whereMonth('created_at', Carbon::now()->subMonth()->month)->count();
            
            if ($lastMonth === 0) return 0;
            return round((($currentMonth - $lastMonth) / $lastMonth) * 100, 1);
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getEvolutionCotisations(): float
    {
        try {
            $currentMonth = Cotisation::whereMonth('created_at', Carbon::now()->month)
                                    ->sum('montant_cotisation');
            $lastMonth = Cotisation::whereMonth('created_at', Carbon::now()->subMonth()->month)
                                 ->sum('montant_cotisation');
            
            if ($lastMonth === 0) return 0;
            return round((($currentMonth - $lastMonth) / $lastMonth) * 100, 1);
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getEvolutionRetraites(): float
    {
        try {
            $currentMonth = Adherent::where('statut', Adherent::STATUS_RETRAITE)
                                  ->whereMonth('updated_at', Carbon::now()->month)
                                  ->count();
            $lastMonth = Adherent::where('statut', Adherent::STATUS_RETRAITE)
                               ->whereMonth('updated_at', Carbon::now()->subMonth()->month)
                               ->count();
            
            if ($lastMonth === 0) return 0;
            return round((($currentMonth - $lastMonth) / $lastMonth) * 100, 1);
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getEvolutionPensions(): float
    {
        try {
            // Pensions du mois actuel
            $currentMonthPreLiq = PreLiquidation::where('etape_actuelle', 'termine')
                                        ->whereMonth('date_paiement', Carbon::now()->month)
                                        ->sum('montant_pension_nette');
            $currentMonthDossiers = DossierPension::where('statut', DossierPension::STATUS_LIQUIDE)
                                        ->whereMonth('date_liquidation', Carbon::now()->month)
                                        ->sum('montant_pension_liquide');
            $currentMonth = $currentMonthPreLiq + $currentMonthDossiers;

            // Pensions du mois précédent
            $lastMonthPreLiq = PreLiquidation::where('etape_actuelle', 'termine')
                                     ->whereMonth('date_paiement', Carbon::now()->subMonth()->month)
                                     ->sum('montant_pension_nette');
            $lastMonthDossiers = DossierPension::where('statut', DossierPension::STATUS_LIQUIDE)
                                     ->whereMonth('date_liquidation', Carbon::now()->subMonth()->month)
                                     ->sum('montant_pension_liquide');
            $lastMonth = $lastMonthPreLiq + $lastMonthDossiers;

            if ($lastMonth === 0) return 0;
            return round((($currentMonth - $lastMonth) / $lastMonth) * 100, 1);
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getTauxRecouvrement(): float
    {
        try {
            $totalCotisations = Cotisation::count();
            $cotisationsPayees = Cotisation::where('statut', Cotisation::STATUS_PAYEE)->count();
            
            if ($totalCotisations === 0) return 0;
            return round(($cotisationsPayees / $totalCotisations) * 100, 1);
        } catch (\Exception $e) {
            return 92.0; // Valeur par défaut
        }
    }

    private function getDelaiTraitement(): int
    {
        try {
            $dossiers = PreLiquidation::where('etape_actuelle', 'termine')
                                    ->whereNotNull('date_reception')
                                    ->whereNotNull('date_paiement')
                                    ->get();
            
            if ($dossiers->isEmpty()) return 15;
            
            $totalJours = $dossiers->sum(function ($dossier) {
                return Carbon::parse($dossier->date_reception)
                           ->diffInDays(Carbon::parse($dossier->date_paiement));
            });
            
            return round($totalJours / $dossiers->count());
        } catch (\Exception $e) {
            return 15; // Valeur par défaut
        }
    }

    private function getSatisfactionClients(): float
    {
        try {
            // Calculer un indicateur de satisfaction basé sur les performances réelles
            $totalDossiers = PreLiquidation::count();
            $dossiersTermines = PreLiquidation::where('etape_actuelle', 'termine')->count();
            $dossiersRejetes = PreLiquidation::where('etape_actuelle', 'rejete')->count();

            if ($totalDossiers === 0) {
                return 95.0; // Valeur par défaut
            }

            // Calculer un score basé sur :
            // - Taux de dossiers terminés avec succès
            // - Faible taux de rejets
            $tauxReussite = ($dossiersTermines / $totalDossiers) * 100;
            $tauxRejet = ($dossiersRejetes / $totalDossiers) * 100;

            // Score de satisfaction estimé
            $satisfaction = min(100, max(70, $tauxReussite - ($tauxRejet * 2) + 10));

            return round($satisfaction, 1);

        } catch (\Exception $e) {
            return 95.0; // Valeur par défaut en cas d'erreur
        }
    }

    private function getDossiersTraites(): int
    {
        try {
            return PreLiquidation::where('etape_actuelle', 'termine')
                                ->whereMonth('date_paiement', Carbon::now()->month)
                                ->count();
        } catch (\Exception $e) {
            return 0;
        }
    }



    /**
     * Export data (placeholder)
     */
    public function exportData(string $type)
    {
        // TODO: Implémenter l'export des données
        return response()->json(['message' => 'Export en cours de développement']);
    }
}
