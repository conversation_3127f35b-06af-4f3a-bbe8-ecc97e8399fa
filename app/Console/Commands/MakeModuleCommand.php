<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class MakeModuleCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'make:module {name : The name of the module}';

    /**
     * The console command description.
     */
    protected $description = 'Create a new module with all necessary files and directories';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $moduleName = Str::studly($this->argument('name'));
        $modulePath = base_path("modules/{$moduleName}");

        if (File::exists($modulePath)) {
            $this->error("Module {$moduleName} already exists!");
            return 1;
        }

        // Create module directories
        $directories = [
            'Controllers',
            'Models',
            'Views',
            'Migrations',
            'Requests',
            'Services',
            'Repositories',
            'lang'
        ];

        foreach ($directories as $directory) {
            File::makeDirectory("{$modulePath}/{$directory}", 0755, true);
        }

        // Create service provider
        $this->createServiceProvider($modulePath, $moduleName);

        // Create routes file
        $this->createRoutesFile($modulePath, $moduleName);

        // Create config file
        $this->createConfigFile($modulePath, $moduleName);

        $this->info("Module {$moduleName} created successfully!");
        $this->info("Don't forget to register the module service provider in config/app.php");

        return 0;
    }

    /**
     * Create service provider for the module
     */
    protected function createServiceProvider(string $modulePath, string $moduleName): void
    {
        $stub = $this->getServiceProviderStub();
        $content = str_replace(['{{ModuleName}}', '{{moduleName}}'], [$moduleName, strtolower($moduleName)], $stub);
        
        File::put("{$modulePath}/{$moduleName}ServiceProvider.php", $content);
    }

    /**
     * Create routes file for the module
     */
    protected function createRoutesFile(string $modulePath, string $moduleName): void
    {
        $stub = $this->getRoutesStub();
        $content = str_replace(['{{ModuleName}}', '{{moduleName}}'], [$moduleName, strtolower($moduleName)], $stub);
        
        File::put("{$modulePath}/routes.php", $content);
    }

    /**
     * Create config file for the module
     */
    protected function createConfigFile(string $modulePath, string $moduleName): void
    {
        $stub = $this->getConfigStub();
        $content = str_replace(['{{ModuleName}}', '{{moduleName}}'], [$moduleName, strtolower($moduleName)], $stub);
        
        File::put("{$modulePath}/config.php", $content);
    }

    /**
     * Get service provider stub
     */
    protected function getServiceProviderStub(): string
    {
        return '<?php

namespace Modules\{{ModuleName}};

use App\Providers\BaseModuleServiceProvider;

class {{ModuleName}}ServiceProvider extends BaseModuleServiceProvider
{
    /**
     * Get module name
     */
    protected function getModuleName(): string
    {
        return "{{ModuleName}}";
    }

    /**
     * Register module services
     */
    protected function registerServices(): void
    {
        // Register module specific services here
    }
}';
    }

    /**
     * Get routes stub
     */
    protected function getRoutesStub(): string
    {
        return '<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| {{ModuleName}} Module Routes
|--------------------------------------------------------------------------
*/

Route::prefix("{{moduleName}}")
    ->name("{{moduleName}}.")
    ->middleware(["web", "auth"])
    ->group(function () {
        // Add your routes here
    });';
    }

    /**
     * Get config stub
     */
    protected function getConfigStub(): string
    {
        return '<?php

return [
    /*
    |--------------------------------------------------------------------------
    | {{ModuleName}} Module Configuration
    |--------------------------------------------------------------------------
    */
    
    "name" => "{{ModuleName}}",
    "description" => "{{ModuleName}} module for CRFM application",
    "version" => "1.0.0",
];';
    }
}
