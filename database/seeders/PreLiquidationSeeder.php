<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Modules\PreLiquidations\Models\PreLiquidation;
use Modules\Adherents\Models\Adherent;
use Carbon\Carbon;

class PreLiquidationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $adherents = Adherent::where('statut', 'actif')->take(50)->get();

        if ($adherents->isEmpty()) {
            $this->command->warn('Aucun adhérent actif trouvé. Veuillez d\'abord exécuter AdherentSeeder.');
            return;
        }

        $etapes = [
            PreLiquidation::ETAPE_RECEPTION,
            PreLiquidation::ETAPE_VERIFICATION,
            PreLiquidation::ETAPE_CALCUL,
            PreLiquidation::ETAPE_VALIDATION,
            PreLiquidation::ETAPE_LIQUIDATION,
            PreLiquidation::ETAPE_TERMINE
        ];

        $statuts = [
            PreLiquidation::STATUT_EN_COURS,
            PreLiquidation::STATUT_PRET,
            PreLiquidation::STATUT_LIQUIDE,
            PreLiquidation::STATUT_PAYE,
            PreLiquidation::STATUT_REJETE
        ];

        $typesPension = [
            PreLiquidation::TYPE_VIEILLESSE,
            PreLiquidation::TYPE_INVALIDITE,
            PreLiquidation::TYPE_SURVIVANT,
            PreLiquidation::TYPE_ANTICIPEE
        ];

        foreach ($adherents as $index => $adherent) {
            // Créer des dossiers avec différents états
            $etape = $etapes[array_rand($etapes)];
            $statut = $statuts[array_rand($statuts)];
            $typePension = $typesPension[array_rand($typesPension)];

            // Calculer les montants basés sur le salaire et la durée de cotisation
            $dureeCotisationMois = rand(120, 480); // Entre 10 et 40 ans
            $salaireReference = $adherent->salaire_base * rand(80, 120) / 100; // Variation du salaire
            $tauxPension = min(75, ($dureeCotisationMois / 12) * 1.875); // Taux progressif
            $montantBrut = ($salaireReference * $tauxPension / 100) * 12; // Montant annuel
            $montantNet = $montantBrut * 0.85; // Après retenues

            // Dates
            $dateCreation = Carbon::now()->subDays(rand(1, 365));
            $dateDepot = $dateCreation->copy()->addDays(rand(1, 30));
            $dateDepartRetraite = Carbon::now()->addDays(rand(-180, 365));

            // Dates de traitement selon l'étape
            $dateReception = $etape !== PreLiquidation::ETAPE_RECEPTION ? $dateDepot->copy()->addDays(rand(1, 5)) : null;
            $dateVerification = in_array($etape, [PreLiquidation::ETAPE_VERIFICATION, PreLiquidation::ETAPE_CALCUL, PreLiquidation::ETAPE_VALIDATION, PreLiquidation::ETAPE_LIQUIDATION, PreLiquidation::ETAPE_PAIEMENT, PreLiquidation::ETAPE_TERMINE]) ?
                ($dateReception ? $dateReception->copy()->addDays(rand(1, 10)) : null) : null;
            $dateCalcul = in_array($etape, [PreLiquidation::ETAPE_CALCUL, PreLiquidation::ETAPE_VALIDATION, PreLiquidation::ETAPE_LIQUIDATION, PreLiquidation::ETAPE_PAIEMENT, PreLiquidation::ETAPE_TERMINE]) ?
                ($dateVerification ? $dateVerification->copy()->addDays(rand(1, 15)) : null) : null;
            $dateValidation = in_array($etape, [PreLiquidation::ETAPE_VALIDATION, PreLiquidation::ETAPE_LIQUIDATION, PreLiquidation::ETAPE_PAIEMENT, PreLiquidation::ETAPE_TERMINE]) ?
                ($dateCalcul ? $dateCalcul->copy()->addDays(rand(1, 7)) : null) : null;
            $dateLiquidation = in_array($etape, [PreLiquidation::ETAPE_LIQUIDATION, PreLiquidation::ETAPE_PAIEMENT, PreLiquidation::ETAPE_TERMINE]) ?
                ($dateValidation ? $dateValidation->copy()->addDays(rand(1, 5)) : null) : null;
            $datePaiement = in_array($etape, [PreLiquidation::ETAPE_PAIEMENT, PreLiquidation::ETAPE_TERMINE]) ?
                ($dateLiquidation ? $dateLiquidation->copy()->addDays(rand(1, 30)) : null) : null;

            PreLiquidation::create([
                'uuid' => \Str::uuid(),
                'numero_dossier' => 'PL' . date('Y') . str_pad($adherent->id * 100 + $index + 1, 4, '0', STR_PAD_LEFT),
                'date_creation' => $dateCreation,
                'date_depot' => $dateDepot,
                'nom_beneficiaire' => $adherent->nom,
                'prenom_beneficiaire' => $adherent->prenoms,
                'date_naissance' => $adherent->date_naissance,
                'lieu_naissance' => $adherent->lieu_naissance,
                'sexe' => $adherent->sexe,
                'numero_securite_sociale' => $adherent->numero_adherent,
                'numero_adherent' => $adherent->numero_adherent,
                'adresse' => $adherent->adresse_domicile,
                'telephone' => $adherent->telephone,
                'email' => $adherent->email,
                'type_pension' => $typePension,
                'date_depart_retraite' => $dateDepartRetraite,
                'date_fin_activite' => $dateDepartRetraite->copy()->subDays(rand(1, 30)),
                'dernier_employeur' => $adherent->employeur,
                'fonction' => $adherent->profession,
                'duree_cotisation_mois' => $dureeCotisationMois,
                'salaire_reference' => $salaireReference,
                'montant_pension_brute' => $montantBrut,
                'montant_pension_nette' => $montantNet,
                'taux_pension' => $tauxPension,
                'coefficient_majoration' => 1.0,
                'etape_actuelle' => $etape,
                'statut' => $statut,
                'date_reception' => $dateReception,
                'date_verification' => $dateVerification,
                'date_calcul' => $dateCalcul,
                'date_validation' => $dateValidation,
                'date_liquidation' => $dateLiquidation,
                'date_paiement' => $datePaiement,
                'user_created_id' => 1,
                'user_assigned_id' => 1,
                'user_validated_id' => $dateValidation ? 1 : null,
                'documents_requis' => [
                    'acte_naissance',
                    'certificat_travail',
                    'releve_cotisations',
                    'piece_identite'
                ],
                'documents_fournis' => rand(0, 1) ? [
                    'acte_naissance',
                    'certificat_travail',
                    'piece_identite'
                ] : [],
                'observations' => $statut === PreLiquidation::STATUT_REJETE ? 'Dossier incomplet' : null,
                'motif_rejet' => $statut === PreLiquidation::STATUT_REJETE ? 'Documents manquants' : null,
                'priorite' => ['normale', 'haute', 'urgente'][rand(0, 2)],
                'dossier_complet' => rand(0, 1) === 1,
            ]);
        }

        $this->command->info('Pré-liquidations créées avec succès.');
    }
}
