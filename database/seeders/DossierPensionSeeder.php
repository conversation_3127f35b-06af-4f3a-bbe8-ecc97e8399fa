<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Modules\Pensions\Models\DossierPension;

use Modules\Adherents\Models\Adherent;
use Carbon\Carbon;

class DossierPensionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $adherents = Adherent::where('statut', 'actif')->take(30)->get();

        if ($adherents->isEmpty()) {
            $this->command->warn('Aucun adhérent actif trouvé. Veuillez d\'abord exécuter AdherentSeeder.');
            return;
        }

        $statuts = [
            DossierPension::STATUS_EN_COURS,
            DossierPension::STATUS_VALIDE,
            DossierPension::STATUS_LIQUIDE,
            DossierPension::STATUS_REJETE,
            DossierPension::STATUS_SUSPENDU
        ];

        $typesPension = [
            DossierPension::TYPE_VIEILLESSE,
            DossierPension::TYPE_INVALIDITE,
            DossierPension::TYPE_SURVIVANT,
            DossierPension::TYPE_ANTICIPEE
        ];

        foreach ($adherents as $index => $adherent) {
            $statut = $statuts[array_rand($statuts)];
            $typePension = $typesPension[array_rand($typesPension)];

            // Calculer les montants
            $dureeCotisationMois = rand(120, 480); // Entre 10 et 40 ans
            $salaireReference = $adherent->salaire_base * rand(80, 120) / 100;
            $tauxLiquidation = min(0.75, ($dureeCotisationMois / 12) * 0.01875); // Taux progressif
            $montantCalcule = ($salaireReference * $tauxLiquidation) * 12; // Montant annuel
            $montantLiquide = $statut === DossierPension::STATUS_LIQUIDE ? $montantCalcule * 0.95 : null;

            // Dates
            $dateDemande = Carbon::now()->subDays(rand(1, 365));
            $dateDepartRetraite = Carbon::now()->addDays(rand(-180, 365));
            $dateLiquidation = $statut === DossierPension::STATUS_LIQUIDE ? 
                $dateDemande->copy()->addDays(rand(30, 120)) : null;

            $dossier = DossierPension::create([
                'uuid' => \Str::uuid(),
                'adherent_id' => $adherent->id,
                'numero_dossier' => 'DP' . date('Y') . str_pad($adherent->id * 100 + $index + 1, 4, '0', STR_PAD_LEFT),
                'type_pension' => $typePension,
                'date_demande' => $dateDemande,
                'date_depart_retraite' => $dateDepartRetraite,
                'date_liquidation' => $dateLiquidation,
                'statut' => $statut,
                'montant_pension_calcule' => $montantCalcule,
                'montant_pension_liquide' => $montantLiquide,
                'duree_cotisation_mois' => $dureeCotisationMois,
                'salaire_reference' => $salaireReference,
                'taux_liquidation' => $tauxLiquidation,
                'observations' => $statut === DossierPension::STATUS_REJETE ? 'Dossier incomplet' : null,
                'created_by' => 1,
                'validated_by' => in_array($statut, [DossierPension::STATUS_VALIDE, DossierPension::STATUS_LIQUIDE]) ? 1 : null,
                'validated_at' => in_array($statut, [DossierPension::STATUS_VALIDE, DossierPension::STATUS_LIQUIDE]) ? 
                    $dateDemande->copy()->addDays(rand(15, 60)) : null,
                'liquidated_by' => $statut === DossierPension::STATUS_LIQUIDE ? 1 : null,
                'liquidated_at' => $dateLiquidation,
            ]);

            // Note: Pré-liquidation sera créée séparément si nécessaire
        }

        $this->command->info('Dossiers de pension créés avec succès.');
    }
}
