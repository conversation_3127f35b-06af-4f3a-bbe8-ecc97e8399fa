<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dossier_pensions', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('adherent_id')->constrained()->onDelete('cascade');

            // Identification
            $table->string('numero_dossier')->unique();
            $table->enum('type_pension', ['vieillesse', 'invalidite', 'survivant', 'anticipee']);

            // Dates importantes
            $table->date('date_demande');
            $table->date('date_depart_retraite');
            $table->date('date_liquidation')->nullable();

            // Statut et workflow
            $table->enum('statut', ['en_cours', 'valide', 'liquide', 'rejete', 'suspendu'])->default('en_cours');

            // Calculs pension
            $table->decimal('montant_pension_calcule', 15, 2)->nullable();
            $table->decimal('montant_pension_liquide', 15, 2)->nullable();
            $table->integer('duree_cotisation_mois')->nullable();
            $table->decimal('salaire_reference', 15, 2)->nullable();
            $table->decimal('taux_liquidation', 5, 4)->nullable(); // ex: 0.6250 pour 62.5%

            // Observations et suivi
            $table->text('observations')->nullable();

            // Audit
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('validated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('validated_at')->nullable();
            $table->foreignId('liquidated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('liquidated_at')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Index
            $table->index(['adherent_id', 'statut']);
            $table->index(['type_pension']);
            $table->index(['date_demande']);
            $table->index(['statut']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dossier_pensions');
    }
};
