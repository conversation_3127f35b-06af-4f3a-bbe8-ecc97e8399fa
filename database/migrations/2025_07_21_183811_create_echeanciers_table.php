<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('echeanciers', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('adherent_id')->constrained()->onDelete('cascade');
            $table->foreignId('cotisation_id')->constrained()->onDelete('cascade');

            $table->string('numero_echeance');
            $table->decimal('montant_echeance', 15, 2);
            $table->date('date_echeance');
            $table->timestamp('date_paiement')->nullable();
            $table->enum('statut', ['en_attente', 'payee', 'en_retard'])->default('en_attente');
            $table->text('observations')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->index(['adherent_id', 'statut']);
            $table->index(['cotisation_id']);
            $table->index(['date_echeance']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('echeanciers');
    }
};
