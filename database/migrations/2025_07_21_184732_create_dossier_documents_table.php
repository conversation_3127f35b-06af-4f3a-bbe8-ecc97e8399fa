<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dossier_documents', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('dossier_pension_id')->constrained()->onDelete('cascade');

            // Type et identification du document
            $table->enum('type_document', [
                'demande_pension', 'certificat_travail', 'bulletins_salaire',
                'acte_naissance', 'cni', 'certificat_medical', 'acte_deces',
                'certificat_mariage', 'rib', 'autre'
            ]);
            $table->string('nom_document');

            // Fichier
            $table->string('chemin_fichier');
            $table->bigInteger('taille_fichier'); // en bytes
            $table->string('type_mime');
            $table->timestamp('date_upload');
            $table->foreignId('uploaded_by')->constrained('users')->onDelete('cascade');

            // Validation et vérification
            $table->boolean('is_required')->default(false);
            $table->boolean('is_verified')->default(false);
            $table->timestamp('date_verification')->nullable();
            $table->foreignId('verified_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('observations')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Index
            $table->index(['dossier_pension_id', 'type_document']);
            $table->index(['is_required', 'is_verified']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dossier_documents');
    }
};
