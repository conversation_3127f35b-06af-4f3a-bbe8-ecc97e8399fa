<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('adherents', function (Blueprint $table) {
            // Ajouter le statut 'en_attente' aux statuts existants
            $table->dropColumn('statut');
        });

        Schema::table('adherents', function (Blueprint $table) {
            $table->enum('statut', ['en_attente', 'actif', 'suspendu', 'radie', 'retraite'])->default('en_attente')->after('date_adhesion');
            $table->timestamp('validated_at')->nullable()->after('observations');
            $table->foreignId('validated_by')->nullable()->constrained('users')->onDelete('set null')->after('validated_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('adherents', function (Blueprint $table) {
            $table->dropForeign(['validated_by']);
            $table->dropColumn(['validated_at', 'validated_by']);
            $table->dropColumn('statut');
        });

        Schema::table('adherents', function (Blueprint $table) {
            $table->enum('statut', ['actif', 'suspendu', 'radie', 'retraite'])->default('actif')->after('date_adhesion');
        });
    }
};
