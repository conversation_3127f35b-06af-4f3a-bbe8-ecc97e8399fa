<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pre_liquidations', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();

            // Informations du dossier
            $table->string('numero_dossier')->unique();
            $table->date('date_creation');
            $table->date('date_depot')->nullable();

            // Informations du bénéficiaire
            $table->string('nom_beneficiaire');
            $table->string('prenom_beneficiaire');
            $table->date('date_naissance');
            $table->string('lieu_naissance')->nullable();
            $table->enum('sexe', ['M', 'F']);
            $table->string('numero_securite_sociale')->nullable();
            $table->string('numero_adherent')->nullable();

            // Informations de contact
            $table->text('adresse')->nullable();
            $table->string('telephone')->nullable();
            $table->string('email')->nullable();

            // Type de pension
            $table->enum('type_pension', ['vieillesse', 'anticipee', 'invalidite', 'survivant', 'orphelin']);
            $table->date('date_depart_retraite');
            $table->date('date_fin_activite')->nullable();

            // Informations professionnelles
            $table->string('dernier_employeur')->nullable();
            $table->string('fonction')->nullable();
            $table->integer('duree_cotisation_mois')->default(0);
            $table->decimal('salaire_reference', 15, 2)->nullable();

            // Calculs de pension
            $table->decimal('montant_pension_brute', 15, 2)->nullable();
            $table->decimal('montant_pension_nette', 15, 2)->nullable();
            $table->decimal('taux_pension', 5, 2)->nullable();
            $table->decimal('coefficient_majoration', 5, 4)->default(1.0000);

            // Workflow et statut
            $table->enum('etape_actuelle', [
                'reception', 'verification', 'calcul', 'validation',
                'liquidation', 'paiement', 'termine', 'rejete'
            ])->default('reception');

            $table->enum('statut', [
                'nouveau', 'en_cours', 'pret', 'liquide',
                'paye', 'suspendu', 'rejete', 'archive'
            ])->default('nouveau');

            // Dates du workflow
            $table->timestamp('date_reception')->nullable();
            $table->timestamp('date_verification')->nullable();
            $table->timestamp('date_calcul')->nullable();
            $table->timestamp('date_validation')->nullable();
            $table->timestamp('date_liquidation')->nullable();
            $table->timestamp('date_paiement')->nullable();

            // Utilisateurs responsables
            $table->foreignId('user_created_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('user_assigned_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('user_validated_id')->nullable()->constrained('users')->onDelete('set null');

            // Documents et observations
            $table->json('documents_requis')->nullable();
            $table->json('documents_fournis')->nullable();
            $table->text('observations')->nullable();
            $table->text('motif_rejet')->nullable();

            // Priorité et urgence
            $table->enum('priorite', ['normale', 'haute', 'urgente'])->default('normale');
            $table->boolean('dossier_complet')->default(false);

            $table->timestamps();
            $table->softDeletes();

            // Index pour les performances
            $table->index(['numero_dossier']);
            $table->index(['nom_beneficiaire', 'prenom_beneficiaire']);
            $table->index(['etape_actuelle', 'statut']);
            $table->index(['type_pension', 'date_depart_retraite']);
            $table->index(['user_assigned_id', 'etape_actuelle']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pre_liquidations');
    }
};
