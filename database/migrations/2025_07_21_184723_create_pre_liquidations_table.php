<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pre_liquidations', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('dossier_pension_id')->constrained()->onDelete('cascade');
            $table->foreignId('adherent_id')->constrained()->onDelete('cascade');

            // Identification
            $table->string('numero_pre_liquidation')->unique();
            $table->date('date_calcul');

            // Calculs de base
            $table->decimal('salaire_reference', 15, 2);
            $table->integer('duree_cotisation_mois');
            $table->decimal('taux_liquidation', 5, 4); // ex: 0.6250 pour 62.5%

            // Montants
            $table->decimal('montant_brut', 15, 2);
            $table->decimal('montant_net', 15, 2);
            $table->decimal('retenues_fiscales', 15, 2)->default(0);
            $table->decimal('retenues_sociales', 15, 2)->default(0);
            $table->decimal('autres_retenues', 15, 2)->default(0);

            // Modalités de versement
            $table->date('date_effet');
            $table->enum('periodicite_versement', ['mensuelle', 'trimestrielle', 'semestrielle', 'annuelle'])->default('mensuelle');
            $table->enum('mode_versement', ['virement', 'cheque', 'especes', 'mobile_money'])->default('virement');

            // Statut et validation
            $table->enum('statut', ['brouillon', 'calcule', 'valide', 'rejete'])->default('brouillon');
            $table->text('observations')->nullable();

            // Audit
            $table->foreignId('calculated_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('validated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('validated_at')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Index
            $table->index(['dossier_pension_id']);
            $table->index(['adherent_id']);
            $table->index(['statut']);
            $table->index(['date_calcul']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pre_liquidations');
    }
};
