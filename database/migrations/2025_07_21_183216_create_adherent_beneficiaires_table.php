<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('adherent_beneficiaires', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('adherent_id')->constrained()->onDelete('cascade');

            $table->string('nom');
            $table->string('prenoms');
            $table->date('date_naissance');
            $table->string('lieu_naissance');
            $table->enum('sexe', ['M', 'F']);
            $table->enum('lien_parente', ['conjoint', 'enfant', 'parent', 'frere_soeur', 'autre']);
            $table->string('telephone')->nullable();
            $table->text('adresse')->nullable();
            $table->decimal('pourcentage_benefice', 5, 2); // ex: 50.00 pour 50%
            $table->boolean('is_active')->default(true);

            $table->timestamps();
            $table->softDeletes();

            $table->index(['adherent_id', 'is_active']);
            $table->index(['lien_parente']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('adherent_beneficiaires');
    }
};
