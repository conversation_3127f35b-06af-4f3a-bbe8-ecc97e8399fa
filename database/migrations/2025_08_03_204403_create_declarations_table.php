<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('declarations', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();

            // Informations de base
            $table->string('numero_declaration')->unique();
            $table->string('employeur');
            $table->string('siret_employeur')->nullable();
            $table->text('adresse_employeur')->nullable();

            // Période de déclaration
            $table->integer('annee');
            $table->integer('mois');
            $table->string('periode'); // Format: "Janvier 2024"

            // Données de la déclaration
            $table->integer('nombre_employes')->default(0);
            $table->decimal('montant_total', 15, 2)->default(0);
            $table->decimal('montant_cotisations_salariales', 15, 2)->default(0);
            $table->decimal('montant_cotisations_patronales', 15, 2)->default(0);

            // Dates importantes
            $table->date('date_reception');
            $table->date('date_echeance')->nullable();
            $table->date('date_traitement')->nullable();
            $table->date('date_validation')->nullable();

            // Statut et workflow
            $table->enum('statut', ['en_attente', 'en_traitement', 'validee', 'rejetee', 'incomplete'])->default('en_attente');
            $table->text('motif_rejet')->nullable();
            $table->text('observations')->nullable();

            // Fichiers et documents
            $table->string('fichier_declaration')->nullable();
            $table->string('fichier_justificatifs')->nullable();
            $table->json('documents_annexes')->nullable();

            // Utilisateurs responsables
            $table->foreignId('user_created_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('user_treated_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('user_validated_id')->nullable()->constrained('users')->onDelete('set null');

            // Métadonnées
            $table->boolean('is_rectificative')->default(false);
            $table->foreignId('declaration_originale_id')->nullable()->constrained('declarations')->onDelete('set null');
            $table->integer('version')->default(1);

            $table->timestamps();
            $table->softDeletes();

            // Index pour les performances
            $table->index(['employeur', 'annee', 'mois']);
            $table->index(['statut', 'date_reception']);
            $table->index(['periode', 'statut']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('declarations');
    }
};
